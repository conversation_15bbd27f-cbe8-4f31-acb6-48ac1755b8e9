# 权益卡订单业务规则

## 权益卡订单状态流转

### 基本状态流转
```
待付款 → 已付款
```

### 异常状态流转
```
待付款 → 已取消
已付款 → 已退款
```

### 状态说明
- **待付款**: 订单创建后，等待用户付款
- **已付款**: 付款完成，权益卡已发放给用户
- **已取消**: 订单被取消（仅限待付款状态）
- **已退款**: 订单已退款，权益卡被回收

## 权益卡订单创建规则

### 基本信息验证
1. **客户信息**: 必须提供有效的客户ID
2. **权益卡类型**: 必须选择有效且启用的权益卡类型
3. **权益卡类型状态**: 权益卡类型必须处于启用状态
4. **订单编号**: 自动生成，格式为 MC + 时间戳 + 4位随机数

### 权益卡类型规则
1. **折扣卡规则**:
   - 必须填写折扣率（0-1之间的小数）
   - 折扣率不能为0或负数
   - 可以设置有效期（天数）
2. **次卡规则**:
   - 必须填写可用次数（正整数）
   - 折扣率强制为0（不享受折扣）
   - 可以设置有效期（天数）
3. **有效期规则**:
   - 有效期为非必填项
   - 如果设置有效期，从购买时间开始计算
   - 未设置有效期的权益卡永久有效

### 价格计算规则
1. **订单金额**: 直接使用权益卡类型的价格
2. **无优惠**: 权益卡订单本身不支持使用其他优惠
3. **固定价格**: 每个权益卡类型有固定的销售价格

## 权益卡发放规则

### 支付成功后自动发放
1. **权益卡创建**: 支付成功后自动创建用户权益卡记录
2. **有效期计算**: 
   - 如果权益卡类型设置了有效天数，从购买时间开始计算到期时间
   - 未设置有效天数的权益卡永久有效（expiryTime为null）
3. **次数设置**:
   - 折扣卡：remainTimes设置为-1（不限次数）
   - 次卡：remainTimes设置为权益卡类型的usageLimit值
4. **状态设置**: 新创建的权益卡状态为'active'（激活）

### 会员状态更新
1. **权益会员**: 购买权益卡成功后，用户会员状态自动更新为权益会员（memberStatus = 1）
2. **状态维护**: 系统定期检查权益卡状态，过期或用完后自动更新会员状态
3. **多卡处理**: 用户可以同时拥有多张权益卡，只要有一张有效就保持权益会员状态

## 权益卡使用规则

### 使用条件检查
1. **状态检查**: 权益卡状态必须为'active'
2. **有效期检查**: 如果设置了有效期，必须在有效期内
3. **次数检查**: 次卡必须有剩余次数（remainTimes > 0）
4. **适用性检查**: 检查权益卡是否适用于当前服务

### 使用扣减规则
1. **折扣卡使用**:
   - 每次使用不扣减次数
   - 按折扣率计算优惠金额
   - 可重复使用直到过期
2. **次卡使用**:
   - 每次使用扣减1次（remainTimes - 1）
   - 当remainTimes为0时，权益卡状态变为'expired'
   - 次卡使用时不享受折扣，主要用于免费或特价服务

### 使用记录
1. **使用记录创建**: 每次使用权益卡都创建使用记录
2. **记录内容**: 包含使用时间、关联订单、使用前后次数等信息
3. **审计追踪**: 提供完整的权益卡使用历史

## 权益卡过期处理

### 自动过期检查
1. **定期检查**: 系统定期检查所有权益卡的有效期
2. **过期更新**: 超过有效期的权益卡自动更新状态为'expired'
3. **会员状态更新**: 如果用户没有其他有效权益卡，会员状态更新为普通会员

### 过期规则
1. **时间过期**: 超过expiryTime的权益卡自动过期
2. **次数用完**: remainTimes为0的次卡自动过期
3. **手动过期**: 管理员可以手动设置权益卡过期

## 退款规则

### 退款条件
1. **状态限制**: 只有"已付款"状态的订单可以申请退款
2. **使用检查**: 已使用的权益卡原则上不允许退款
3. **管理员权限**: 管理员可以对任何状态的订单进行强制退款

### 退款流程
1. **申请退款**: 用户或管理员申请退款
2. **状态变更**: 订单状态变为"已退款"
3. **权益卡回收**: 回收已发放的权益卡（状态变为'expired'）
4. **会员状态检查**: 检查用户是否还有其他有效权益卡，决定是否降级会员状态
5. **退款处理**: 执行微信退款操作

### 退款金额计算
1. **全额退款**: 退还用户实际支付的全部金额
2. **部分退款**: 根据权益卡使用情况计算退款金额
3. **使用扣减**: 已使用的权益卡可能扣减相应费用

## 管理员操作规则

### 管理员权限
1. **最大权限**: 管理员对权益卡订单拥有最大操作权限
2. **任何状态**: 可以对任何状态的订单进行操作
3. **强制操作**: 可以强制退款、删除等操作

### 权益卡管理
1. **手动发放**: 管理员可以手动为用户发放权益卡
2. **禁用权益卡**: 管理员可以禁用用户的权益卡
3. **撤销权益卡**: 管理员可以撤销权益卡并清零剩余次数
4. **操作记录**: 所有管理员操作都会记录操作日志

### 数据一致性
1. **事务处理**: 关键操作使用数据库事务确保一致性
2. **状态同步**: 确保订单状态与权益卡状态的一致性
3. **会员状态**: 确保用户会员状态与权益卡状态的一致性

## 特殊场景处理

### 支付异常处理
1. **微信支付成功但本地更新失败**:
   - 提供手动同步接口
   - 防止重复发放权益卡
   - 记录异常日志
2. **重复支付防护**:
   - 检查订单支付状态
   - 防止重复支付操作
   - 记录支付历史

### 并发处理
1. **订单创建**: 使用数据库锁防止重复创建
2. **状态更新**: 确保状态更新的原子性
3. **权益卡发放**: 防止重复发放权益卡

### 数据恢复
1. **订单状态恢复**: 提供状态回滚机制
2. **支付状态同步**: 定期同步微信支付状态
3. **权益卡修复**: 提供权益卡数据修复工具

## 性能优化规则

### 查询优化
1. **索引优化**: 在常用查询字段上建立索引
2. **关联查询**: 优化权益卡类型和客户信息的关联查询
3. **分页查询**: 大数据量查询必须使用分页

### 缓存策略
1. **权益卡类型**: 缓存启用的权益卡类型信息
2. **用户权益卡**: 缓存用户的有效权益卡列表
3. **计算结果**: 缓存权益卡适用性计算结果

### 数据库事务
1. **订单创建**: 订单创建和权益卡发放使用事务
2. **退款操作**: 退款和权益卡回收使用事务
3. **状态更新**: 批量状态更新使用事务

## 监控和告警

### 业务监控
1. **订单量监控**: 监控权益卡订单创建和支付量
2. **异常订单**: 监控长时间未支付的订单
3. **退款率**: 监控权益卡订单退款率

### 系统监控
1. **接口性能**: 监控关键接口的响应时间
2. **错误率**: 监控接口错误率
3. **支付成功率**: 监控微信支付成功率

### 告警机制
1. **支付异常**: 支付失败率过高时告警
2. **权益卡异常**: 权益卡发放失败时告警
3. **数据不一致**: 发现数据不一致时告警
