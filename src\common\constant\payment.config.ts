/**
 * 微信支付相关配置
 */
export const PaymentConfig = {
  /**
   * 支付有效期配置（单位：分钟）
   */
  PAYMENT_EXPIRE_MINUTES: {
    // 服务订单支付有效期
    SERVICE_ORDER: 30,
    // 权益卡订单支付有效期
    MEMBERSHIP_CARD_ORDER: 30,
    // 代金券订单支付有效期
    COUPON_ORDER: 30,
    // 追加服务订单支付有效期
    ADDITIONAL_SERVICE_ORDER: 30,
  },

  /**
   * 支付超时相关错误码和关键词
   */
  TIMEOUT_ERROR_INDICATORS: [
    'INVALID_REQUEST',
    'time_expire',
    '超时',
    '过期',
    'ORDERNOTEXIST',
    '订单不存在',
  ],

  /**
   * 获取支付有效期（分钟）
   * @param orderType 订单类型
   * @returns 有效期分钟数
   */
  getPaymentExpireMinutes(
    orderType: 'service' | 'membership_card' | 'coupon' | 'additional_service'
  ): number {
    switch (orderType) {
      case 'service':
        return this.PAYMENT_EXPIRE_MINUTES.SERVICE_ORDER;
      case 'membership_card':
        return this.PAYMENT_EXPIRE_MINUTES.MEMBERSHIP_CARD_ORDER;
      case 'coupon':
        return this.PAYMENT_EXPIRE_MINUTES.COUPON_ORDER;
      case 'additional_service':
        return this.PAYMENT_EXPIRE_MINUTES.ADDITIONAL_SERVICE_ORDER;
      default:
        return this.PAYMENT_EXPIRE_MINUTES.SERVICE_ORDER;
    }
  },

  /**
   * 生成支付过期时间
   * @param orderType 订单类型
   * @returns 过期时间对象
   */
  generateExpireTime(
    orderType: 'service' | 'membership_card' | 'coupon' | 'additional_service'
  ) {
    const expireMinutes = this.getPaymentExpireMinutes(orderType);
    const now = new Date();
    const expireTime = new Date(now.getTime() + 1000 * 60 * expireMinutes);

    // 正确处理时区转换为北京时间
    const beijingTime = new Date(expireTime.getTime() + 8 * 60 * 60 * 1000);
    const time_expire = beijingTime.toISOString().replace('Z', '+08:00');

    return {
      now,
      expireTime,
      expireMinutes,
      time_expire,
      beijingTimeStr: beijingTime.toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
      }),
    };
  },

  /**
   * 检查是否为支付超时相关错误
   * @param error 错误对象
   * @returns 是否为超时错误
   */
  isTimeoutError(error: any): boolean {
    const errorCode = error.response?.data?.code;
    const errorMessage = error.response?.data?.message || '';

    return this.TIMEOUT_ERROR_INDICATORS.some(
      indicator => errorCode === indicator || errorMessage.includes(indicator)
    );
  },
};
