import { Rule, RuleType } from '@midwayjs/validate';

/**
 * 订单统计查询DTO
 */
export class OrderStatisticsQueryDTO {
  @Rule(RuleType.date().optional())
  startDate?: string;

  @Rule(RuleType.date().optional())
  endDate?: string;

  @Rule(RuleType.string().valid('day', 'week', 'month').optional())
  groupBy?: 'day' | 'week' | 'month';

  @Rule(RuleType.number().integer().min(1).optional())
  current?: number;

  @Rule(RuleType.number().integer().min(1).max(100).optional())
  pageSize?: number;
}

/**
 * 员工订单统计查询DTO
 */
export class EmployeeOrderStatisticsQueryDTO extends OrderStatisticsQueryDTO {
  @Rule(RuleType.number().integer().optional())
  employeeId?: number;

  @Rule(
    RuleType.string().valid('orderCount', 'totalAmount', 'rating').optional()
  )
  sortBy?: 'orderCount' | 'totalAmount' | 'rating';

  @Rule(RuleType.string().valid('asc', 'desc').optional())
  sortOrder?: 'asc' | 'desc';
}

/**
 * 客户订单统计查询DTO
 */
export class CustomerOrderStatisticsQueryDTO extends OrderStatisticsQueryDTO {
  @Rule(RuleType.number().integer().optional())
  customerId?: number;

  @Rule(
    RuleType.string().valid('orderCount', 'totalAmount', 'avgAmount').optional()
  )
  sortBy?: 'orderCount' | 'totalAmount' | 'avgAmount';

  @Rule(RuleType.string().valid('asc', 'desc').optional())
  sortOrder?: 'asc' | 'desc';
}

/**
 * 时段统计查询DTO
 */
export class TimePeriodStatisticsQueryDTO extends OrderStatisticsQueryDTO {
  @Rule(RuleType.string().valid('hour', 'weekday').optional())
  periodType?: 'hour' | 'weekday';
}

/**
 * 用户订单原价统计查询DTO
 */
export class CustomerOriginalPriceStatisticsQueryDTO {
  @Rule(RuleType.number().integer().required())
  customerId: number;

  @Rule(RuleType.date().optional())
  startDate?: string;

  @Rule(RuleType.date().optional())
  endDate?: string;
}

/**
 * 用户有效订单列表查询DTO
 */
export class CustomerValidOrderListQueryDTO {
  @Rule(RuleType.number().integer().required())
  customerId: number;

  @Rule(RuleType.date().optional())
  startDate?: string;

  @Rule(RuleType.date().optional())
  endDate?: string;

  @Rule(RuleType.number().integer().min(1).optional())
  current?: number;

  @Rule(RuleType.number().integer().min(1).max(100).optional())
  pageSize?: number;

  @Rule(
    RuleType.string()
      .valid('orderTime', 'serviceTime', 'originalPrice', 'totalFee')
      .optional()
  )
  sortBy?: 'orderTime' | 'serviceTime' | 'originalPrice' | 'totalFee';

  @Rule(RuleType.string().valid('asc', 'desc').optional())
  sortOrder?: 'asc' | 'desc';
}
