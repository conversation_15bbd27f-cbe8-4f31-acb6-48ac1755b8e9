# API接口文档总览

## 概述

本目录包含宠物服务管理系统的所有API接口文档，按功能模块分类组织。所有接口文档都采用精简格式，只包含核心的接口定义信息。

## 统一返回格式

### 成功响应
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    // 具体业务数据
  }
}
```

### 错误响应
```json
{
  "errCode": 400,
  "msg": "错误信息"
}
```

## 认证说明

所有API接口都需要在请求头中携带认证信息：
```
Authorization: Bearer <token>
```

## 分页参数

支持分页的接口统一使用以下参数：
- `page`: 页码，从1开始
- `pageSize`: 每页数量，默认10，最大100

## 文档结构

### 核心业务模块
- [订单接口](./订单接口.md) - 订单管理、追加服务、服务计时等
- [权益卡订单接口](./权益卡订单接口.md) - 权益卡购买、支付、管理等
- [代金券订单接口](./代金券订单接口.md) - 代金券购买、支付、管理等
- [客户接口](./客户接口.md) - 客户管理、会员权益等
- [员工接口](./员工接口.md) - 员工管理、出车拍照、职位权限、追加服务管理等
- [服务接口](./服务接口.md) - 服务管理、照片墙、活动管理等

### 支持功能模块
- [投诉建议接口](./投诉建议接口.md) - 投诉建议管理
- [系统管理接口](./系统管理接口.md) - 权限管理、轮播图等
- [通用接口](./通用接口.md) - 文件上传、位置服务等

### 管理端专用模块
- [管理端权益卡订单接口](./管理端权益卡订单接口.md) - 管理端权益卡订单管理、用户权益卡管理等
- [管理端代金券订单接口](./管理端代金券订单接口.md) - 管理端代金券订单管理、用户代金券管理等

### 统计分析模块
- [订单统计接口](./订单统计接口.md) - 订单概览、趋势分析、状态分布、待办订单统计等
- [用户数据统计接口](./用户数据统计接口.md) - 用户概览、性别分布、会员状态、注册趋势等统计
- [用户订单原价统计接口](./用户订单原价统计接口.md) - 用户订单原价和有效订单统计
- [收入统计接口](./收入统计接口.md) - 收入概览、趋势分析、服务维度统计、员工维度统计

## 接口命名规范

### 路径规范
- 用户端接口：`/api/user/*`
- 员工端接口：`/api/employee/*`
- 管理端接口：`/api/admin/*`

### HTTP方法
- `GET` - 查询数据
- `POST` - 创建数据
- `PUT` - 更新数据（完整更新）
- `PATCH` - 更新数据（部分更新）
- `DELETE` - 删除数据

## 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 最近更新

### 2025-07-14 追加服务实体关联修复
- 修复了 `AdditionalServiceOrderDetail` 实体的外键关联问题
- 将外键从错误的 `Service` 表改为正确的 `AdditionalService` 表
- 优化了追加服务相关接口的查询性能
- 详细说明请参考：[追加服务实体关联修复说明](../追加服务实体关联修复说明.md)
