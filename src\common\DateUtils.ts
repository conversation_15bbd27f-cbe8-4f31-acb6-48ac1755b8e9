import { CustomError } from '../error/custom.error';

/**
 * 日期工具类
 */
export class DateUtils {
  /**
   * 验证并解析日期字符串
   * @param dateStr 日期字符串
   * @param isEndDate 是否为结束日期（会自动添加时间为当天结束）
   * @param fieldName 字段名称，用于错误提示
   * @returns 解析后的Date对象
   */
  static validateAndParseDate(
    dateStr: string,
    isEndDate = false,
    fieldName = '日期'
  ): Date {
    if (!dateStr) {
      throw new CustomError(`${fieldName}参数不能为空`, 400);
    }

    // 支持的日期格式：YYYY-MM-DD 或 YYYY-MM-DD HH:mm:ss
    const dateRegex = /^\d{4}-\d{2}-\d{2}(\s\d{2}:\d{2}:\d{2})?$/;
    if (!dateRegex.test(dateStr)) {
      throw new CustomError(
        `${fieldName}格式错误，请使用 YYYY-MM-DD 或 YYYY-MM-DD HH:mm:ss 格式`,
        400
      );
    }

    let date: Date;
    if (isEndDate && !dateStr.includes(' ')) {
      // 如果是结束日期且只有日期部分，自动添加时间为当天结束
      date = new Date(dateStr + ' 23:59:59');
    } else {
      date = new Date(dateStr);
    }

    // 验证日期是否有效
    if (isNaN(date.getTime())) {
      throw new CustomError(`无效的${fieldName}值`, 400);
    }

    return date;
  }

  /**
   * 安全地解析日期字符串，不抛出异常
   * @param dateStr 日期字符串
   * @param isEndDate 是否为结束日期
   * @returns 解析后的Date对象，如果解析失败返回null
   */
  static safeParseDate(dateStr: string, isEndDate = false): Date | null {
    try {
      return this.validateAndParseDate(dateStr, isEndDate);
    } catch {
      return null;
    }
  }

  /**
   * 构建日期范围查询条件（MongoDB风格）
   * @param startDate 开始日期字符串
   * @param endDate 结束日期字符串
   * @param fieldName 数据库字段名，默认为 'createdAt'
   * @returns 查询条件对象
   */
  static buildDateRangeCondition(
    startDate?: string,
    endDate?: string,
    fieldName = 'createdAt'
  ): any {
    if (!startDate && !endDate) {
      return {};
    }

    const condition: any = {};
    condition[fieldName] = {};

    if (startDate) {
      condition[fieldName].$gte = this.validateAndParseDate(
        startDate,
        false,
        '开始日期'
      );
    }

    if (endDate) {
      condition[fieldName].$lte = this.validateAndParseDate(
        endDate,
        true,
        '结束日期'
      );
    }

    return condition;
  }

  /**
   * 构建Sequelize Op日期范围查询条件
   * @param Op Sequelize操作符
   * @param startDate 开始日期字符串
   * @param endDate 结束日期字符串
   * @param fieldName 数据库字段名，默认为 'createdAt'
   * @returns 查询条件对象
   */
  static buildSequelizeDateRangeCondition(
    Op: any,
    startDate?: string,
    endDate?: string,
    fieldName = 'createdAt'
  ): any {
    if (!startDate && !endDate) {
      return {};
    }

    const condition: any = {};
    condition[fieldName] = {};

    if (startDate) {
      condition[fieldName][Op.gte] = this.validateAndParseDate(
        startDate,
        false,
        '开始日期'
      );
    }

    if (endDate) {
      condition[fieldName][Op.lte] = this.validateAndParseDate(
        endDate,
        true,
        '结束日期'
      );
    }

    return condition;
  }

  /**
   * 获取今天的开始和结束时间
   * @returns {start: Date, end: Date}
   */
  static getTodayRange(): { start: Date; end: Date } {
    const today = new Date();
    const start = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate(),
      0,
      0,
      0,
      0
    );
    const end = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate(),
      23,
      59,
      59,
      999
    );
    return { start, end };
  }

  /**
   * 获取指定日期的开始和结束时间
   * @param dateStr 日期字符串 YYYY-MM-DD
   * @returns {start: Date, end: Date}
   */
  static getDayRange(dateStr: string): { start: Date; end: Date } {
    const date = this.validateAndParseDate(dateStr, false, '日期');
    const start = new Date(
      date.getFullYear(),
      date.getMonth(),
      date.getDate(),
      0,
      0,
      0,
      0
    );
    const end = new Date(
      date.getFullYear(),
      date.getMonth(),
      date.getDate(),
      23,
      59,
      59,
      999
    );
    return { start, end };
  }
}
