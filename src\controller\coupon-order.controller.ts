import {
  Controller,
  Get,
  Post,
  Put,
  Del,
  Inject,
  Param,
  Body,
  Query,
} from '@midwayjs/core';
import { Op } from 'sequelize';
import { CouponOrderService } from '../service/coupon-order.service';
import { CouponOrderStatus } from '../entity/coupon-order.entity';
import { CustomError } from '../error/custom.error';
import { Customer } from '../entity/customer.entity';

@Controller('/coupon-orders')
export class CouponOrderController {
  @Inject()
  service: CouponOrderService;

  @Get('/all', { summary: '查询所有代金券订单列表' })
  async findAll(
    @Query('status') status: string,
    @Query('current') current: number,
    @Query('pageSize') pageSize: number,
    @Query('sn') sn: string,
    @Query('customerInfo') customerInfo: string
  ) {
    const query: any = {};
    if (status) {
      query.status = status.split(',');
    }

    // 处理订单编号模糊查询
    if (sn) {
      query.sn = { [Op.like]: `%${sn}%` };
    }

    // 构建include选项，处理用户信息模糊查询
    const includeOptions: any[] = [
      {
        model: Customer,
        as: 'customer',
        where: customerInfo
          ? {
              [Op.or]: [
                { nickname: { [Op.like]: `%${customerInfo}%` } },
                { phone: { [Op.like]: `%${customerInfo}%` } },
              ],
            }
          : undefined,
        required: !!customerInfo, // 如果有customerInfo查询条件，则必须匹配
      },
      'coupon',
    ];

    return await this.service.findAll({
      query,
      offset: (current - 1) * pageSize,
      limit: pageSize,
      order: [['updatedAt', 'DESC']],
      include: includeOptions,
    });
  }

  @Get('/', { summary: '查询代金券订单列表' })
  async index(
    @Query('customerId') customerId: number,
    @Query('status') status: string,
    @Query('current') current: number,
    @Query('pageSize') pageSize: number
  ) {
    if (!customerId) {
      throw new CustomError('用户ID不能为空');
    }

    let statusArray: CouponOrderStatus[] = [];
    if (status) {
      statusArray = status.split(',') as CouponOrderStatus[];
    }

    return await this.service.findCustomerOrders(
      customerId,
      statusArray,
      current,
      pageSize
    );
  }

  @Get('/:id', { summary: '按ID查询代金券订单' })
  async show(@Param('id') id: number) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定代金券订单');
    }
    return res;
  }

  @Post('/', { summary: '创建代金券订单' })
  async create(
    @Body()
    body: {
      customerId: number;
      couponId: number;
      remark?: string;
    }
  ) {
    const { customerId, couponId, remark } = body;
    if (!customerId) {
      throw new CustomError('用户ID不能为空');
    }
    if (!couponId) {
      throw new CustomError('代金券ID不能为空');
    }

    return await this.service.createOrder(customerId, couponId, remark);
  }

  @Post('/:sn/pay', { summary: '支付代金券订单' })
  async pay(@Param('sn') sn: string, @Body('customerId') customerId: number) {
    if (!customerId) {
      throw new CustomError('用户ID不能为空');
    }

    return await this.service.payOrder(customerId, sn);
  }

  @Put('/:sn/cancel', { summary: '取消代金券订单' })
  async cancel(
    @Param('sn') sn: string,
    @Body('customerId') customerId: number
  ) {
    if (!customerId) {
      throw new CustomError('用户ID不能为空');
    }

    return await this.service.cancelOrder(customerId, sn);
  }

  @Del('/:id', { summary: '删除代金券订单' })
  async delete(
    @Param('id') id: number,
    @Body('customerId') customerId: number
  ) {
    if (!customerId) {
      throw new CustomError('用户ID不能为空');
    }

    return await this.service.deleteOrder(customerId, id);
  }
}
