import { Middleware, IMiddleware, Logger, ILogger } from '@midwayjs/core';
import { NextFunction, Context } from '@midwayjs/koa';
import { Employee } from '../entity';
import { CustomError } from '../error/custom.error';

/**
 * 员工状态检查中间件
 * 检查员工是否已离职，离职的员工无法访问业务相关接口
 */
@Middleware()
export class EmployeeStatusMiddleware
  implements IMiddleware<Context, NextFunction>
{
  @Logger()
  logger: ILogger;

  resolve() {
    return async (ctx: Context, next: NextFunction) => {
      // 只对已认证的employee用户进行状态检查
      if (!ctx.state.user || ctx.state.user.userType !== 'employee') {
        return await next();
      }

      // 检查是否是需要状态验证的路径
      if (!this.shouldCheckStatus(ctx)) {
        return await next();
      }

      try {
        const employeeId = ctx.state.user.userId;
        const employee = await Employee.findByPk(employeeId, {
          attributes: ['id', 'status', 'name'],
        });

        if (!employee) {
          throw new CustomError('员工不存在', 404);
        }

        // 检查员工是否已离职 (status: 0-离职 1-在职)
        if (employee.status === 0) {
          throw new CustomError('员工已离职，无法进行此操作', 403);
        }
      } catch (error) {
        if (error instanceof CustomError) {
          throw error;
        }
        this.logger.error('检查员工状态失败:', error);
        throw new CustomError('员工状态验证失败', 500);
      }

      await next();
    };
  }

  /**
   * 判断是否需要检查员工状态
   * @param ctx 上下文
   */
  private shouldCheckStatus(ctx: Context): boolean {
    const path = ctx.path;

    // 需要检查状态的路径模式（员工业务相关接口）
    const checkPaths = [
      '/orders/', // 订单操作（接单、派单、出发等）
      '/employee-checkins', // 员工打卡
      '/employees/', // 员工相关操作
    ];

    // 排除不需要检查的路径
    const excludePaths = [
      '/employees/statistics', // 统计查询可以访问
      '/employees/revenue', // 收入查询可以访问
    ];

    // 如果是排除路径，不检查状态
    if (excludePaths.some(pattern => path.startsWith(pattern))) {
      return false;
    }

    return checkPaths.some(pattern => path.includes(pattern));
  }

  static getName(): string {
    return 'EMPLOYEE_STATUS';
  }
}
