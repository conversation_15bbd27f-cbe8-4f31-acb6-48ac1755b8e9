import {
  Controller,
  Get,
  Post,
  Del,
  Inject,
  Param,
  Query,
  Body,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { CustomError } from '../../error/custom.error';
import { OrderService } from '../../service/order.service';
import { AdditionalServiceOrderService } from '../../service/additional-service-order.service';
import {
  Order,
  OrderDetail,
  AdditionalServiceOrder,
  AdditionalServiceOrderDetail,
  AdditionalServiceDiscountInfo,
  AdditionalService,
  Customer,
  Employee,
} from '../../entity';
import { AdditionalServiceOrderStatus } from '../../entity/additional-service-order.entity';

@Controller('/admin/orders')
export class OrderAdminController {
  @Inject()
  ctx: Context;

  @Inject()
  orderService: OrderService;

  @Inject()
  additionalServiceOrderService: AdditionalServiceOrderService;

  @Get('/:orderId/additional-services', {
    summary: '管理端查询订单追加服务信息',
  })
  async getOrderAdditionalServices(@Param('orderId') orderId: number) {
    // 查询主订单信息
    const order = await Order.findByPk(orderId, {
      include: [
        {
          model: OrderDetail,
          include: [
            {
              model: AdditionalServiceOrder,
              include: [
                {
                  model: AdditionalServiceOrderDetail,
                  include: [
                    {
                      model: AdditionalService,
                      attributes: ['id', 'name', 'needDurationTracking'],
                    },
                  ],
                },
                {
                  model: AdditionalServiceDiscountInfo,
                },
              ],
            },
          ],
        },
        {
          model: Customer,
          attributes: ['id', 'nickname', 'phone'],
        },
        {
          model: Employee,
          attributes: ['id', 'name', 'phone'],
        },
      ],
    });

    if (!order) {
      throw new CustomError('订单不存在');
    }

    // 收集所有追加服务订单
    const allAdditionalServices = [];
    order.orderDetails?.forEach(detail => {
      if (detail.additionalServiceOrders) {
        allAdditionalServices.push(...detail.additionalServiceOrders);
      }
    });

    // 计算追加服务汇总信息
    const summary = {
      totalCount: allAdditionalServices.length,
      totalAmount: allAdditionalServices.reduce(
        (sum, service) => sum + Number(service.totalFee),
        0
      ),
      totalOriginalPrice: allAdditionalServices.reduce(
        (sum, service) => sum + Number(service.originalPrice),
        0
      ),
      totalDeduction: allAdditionalServices.reduce(
        (sum, service) =>
          sum + Number(service.cardDeduction) + Number(service.couponDeduction),
        0
      ),
      paidCount: allAdditionalServices.filter(
        service => service.status === AdditionalServiceOrderStatus.PAID
      ).length,
      paidAmount: allAdditionalServices
        .filter(service => service.status === AdditionalServiceOrderStatus.PAID)
        .reduce((sum, service) => sum + Number(service.totalFee), 0),
      pendingCount: allAdditionalServices.filter(service =>
        [
          AdditionalServiceOrderStatus.PENDING_CONFIRM,
          AdditionalServiceOrderStatus.CONFIRMED,
        ].includes(service.status)
      ).length,
      pendingAmount: allAdditionalServices
        .filter(service =>
          [
            AdditionalServiceOrderStatus.PENDING_CONFIRM,
            AdditionalServiceOrderStatus.CONFIRMED,
          ].includes(service.status)
        )
        .reduce((sum, service) => sum + Number(service.totalFee), 0),
    };

    // 格式化追加服务列表
    const additionalServices = allAdditionalServices.map(service => ({
      id: service.id,
      sn: service.sn,
      status: service.status,
      originalPrice: service.originalPrice,
      totalFee: service.totalFee,
      cardDeduction: service.cardDeduction,
      couponDeduction: service.couponDeduction,
      payTime: service.payTime,
      confirmTime: service.confirmTime,
      rejectReason: service.rejectReason,
      details:
        service.details?.map(detail => ({
          serviceName: detail.serviceName,
          servicePrice: detail.servicePrice,
          quantity: detail.quantity,
        })) || [],
      discountInfos:
        service.discountInfos?.map(discount => ({
          discountType: discount.discountType,
          discountAmount: discount.discountAmount,
        })) || [],
      createdAt: service.createdAt,
    }));

    return {
      orderId: order.id,
      orderSn: order.sn,
      additionalServiceSummary: summary,
      additionalServices: additionalServices.sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      ),
    };
  }

  @Get('/', { summary: '管理端订单列表（包含追加服务信息）' })
  async getOrdersWithAdditionalServices(
    @Query('current') current = 1,
    @Query('pageSize') pageSize = 20,
    @Query('includeAdditionalServices') includeAdditionalServices = true
  ) {
    // 这里可以复用现有的订单查询逻辑，并在结果中添加追加服务摘要信息
    // 为了简化，这里只提供基本的查询逻辑

    const includeOptions = [
      {
        model: Customer,
        attributes: ['id', 'nickname', 'phone'],
      },
      {
        model: Employee,
        attributes: ['id', 'name', 'phone'],
      },
    ];

    // 如果需要包含追加服务信息，添加相关的include
    if (includeAdditionalServices) {
      includeOptions.push({
        model: OrderDetail,
        include: [
          {
            model: AdditionalServiceOrder,
            attributes: [
              'id',
              'status',
              'totalFee',
              'originalPrice',
              'createdAt',
            ],
          },
        ],
      } as any);
    }

    const { count, rows } = await Order.findAndCountAll({
      include: includeOptions,
      order: [['updatedAt', 'DESC']],
      offset: (current - 1) * pageSize,
      limit: pageSize,
    });

    // 格式化结果，添加追加服务摘要信息
    const list = rows.map(order => {
      const baseOrder = {
        id: order.id,
        sn: order.sn,
        status: order.status,
        totalFee: order.totalFee,
        customer: order.customer,
        employee: order.employee,
        createdAt: order.createdAt,
      };

      if (includeAdditionalServices && order.orderDetails) {
        // 收集所有追加服务订单
        const allAdditionalServices = [];
        order.orderDetails.forEach(detail => {
          if (detail.additionalServiceOrders) {
            allAdditionalServices.push(...detail.additionalServiceOrders);
          }
        });

        const additionalServiceSummary = {
          hasAdditionalServices: allAdditionalServices.length > 0,
          totalCount: allAdditionalServices.length,
          totalAmount: allAdditionalServices.reduce(
            (sum, service) => sum + Number(service.totalFee),
            0
          ),
          paidCount: allAdditionalServices.filter(
            service => service.status === AdditionalServiceOrderStatus.PAID
          ).length,
          paidAmount: allAdditionalServices
            .filter(
              service => service.status === AdditionalServiceOrderStatus.PAID
            )
            .reduce((sum, service) => sum + Number(service.totalFee), 0),
          pendingCount: allAdditionalServices.filter(service =>
            [
              AdditionalServiceOrderStatus.PENDING_CONFIRM,
              AdditionalServiceOrderStatus.CONFIRMED,
            ].includes(service.status)
          ).length,
          pendingAmount: allAdditionalServices
            .filter(service =>
              [
                AdditionalServiceOrderStatus.PENDING_CONFIRM,
                AdditionalServiceOrderStatus.CONFIRMED,
              ].includes(service.status)
            )
            .reduce((sum, service) => sum + Number(service.totalFee), 0),
          lastAdditionalServiceTime:
            allAdditionalServices.length > 0
              ? Math.max(
                  ...allAdditionalServices.map(service =>
                    new Date(service.createdAt).getTime()
                  )
                )
              : null,
        };

        return {
          ...baseOrder,
          additionalServiceSummary,
        };
      }

      return baseOrder;
    });

    return {
      list,
      total: count,
      current,
      pageSize,
    };
  }

  @Post('/:orderId/admin-cancel', { summary: '管理员取消订单（任何状态）' })
  async adminCancelOrder(
    @Param('orderId') orderId: number,
    @Body() body: { operatorId: number; reason?: string }
  ) {
    if (!body.operatorId) {
      throw new CustomError('操作员ID不能为空', 400);
    }

    const result = await this.orderService.adminCancelOrder(
      orderId,
      body.operatorId,
      body.reason
    );
    return result;
  }

  @Post('/sn/:sn/admin-apply-refund', { summary: '管理员申请退款（任何状态）' })
  async adminApplyRefund(
    @Param('sn') sn: string,
    @Body() body: { operatorId: number; reason?: string }
  ) {
    if (!body.operatorId) {
      throw new CustomError('操作员ID不能为空', 400);
    }

    const result = await this.orderService.adminApplyRefund(
      sn,
      body.operatorId,
      body.reason
    );
    return result;
  }

  @Del('/:orderId/admin-delete', { summary: '管理员删除订单（任何状态）' })
  async adminDeleteOrder(
    @Param('orderId') orderId: number,
    @Body() body: { operatorId: number; reason?: string }
  ) {
    if (!body.operatorId) {
      throw new CustomError('操作员ID不能为空', 400);
    }

    const result = await this.orderService.adminDeleteOrder(
      orderId,
      body.operatorId,
      body.reason
    );
    return { result };
  }

  @Get('/:orderId/refund-info', {
    summary: '获取订单退款信息（主订单+追加服务）',
  })
  async getOrderRefundInfo(@Param('orderId') orderId: number) {
    // 查询主订单信息
    const order = await Order.findByPk(orderId, {
      include: [
        {
          model: OrderDetail,
          include: [
            {
              model: AdditionalServiceOrder,
              include: [
                {
                  model: AdditionalServiceOrderDetail,
                  include: [
                    {
                      model: AdditionalService,
                      attributes: ['id', 'name'],
                    },
                  ],
                },
                {
                  model: AdditionalServiceDiscountInfo,
                },
              ],
            },
          ],
        },
        {
          model: Customer,
          attributes: ['id', 'nickname', 'phone'],
        },
        {
          model: Employee,
          attributes: ['id', 'name', 'phone'],
        },
      ],
    });

    if (!order) {
      throw new CustomError('订单不存在');
    }

    // 主订单信息
    const mainOrderInfo = {
      type: 'main_order',
      id: order.sn,
      orderSn: order.sn,
      status: order.status,
      totalFee: Number(order.totalFee),
      originalPrice: Number(order.totalFee), // 主订单的原价等于总费用
      canRefund: true, // 管理员可以退款任何状态的订单
      refundableAmount: Number(order.totalFee),
    };

    // 收集所有追加服务订单
    const additionalServices = [];
    order.orderDetails?.forEach(detail => {
      if (detail.additionalServiceOrders) {
        detail.additionalServiceOrders.forEach(addOrder => {
          const serviceNames =
            addOrder.details?.map(d => d.serviceName).join(', ') || '';
          const hasDiscounts =
            addOrder.discountInfos && addOrder.discountInfos.length > 0;

          additionalServices.push({
            type: 'additional_service',
            id: addOrder.id,
            orderSn: addOrder.sn,
            status: addOrder.status,
            totalFee: Number(addOrder.totalFee),
            originalPrice: Number(addOrder.originalPrice),
            cardDeduction: Number(addOrder.cardDeduction),
            couponDeduction: Number(addOrder.couponDeduction),
            serviceNames,
            hasDiscounts,
            canRefund: true, // 管理员可以退款任何状态的追加服务
            refundableAmount: Number(addOrder.totalFee),
            createdAt: addOrder.createdAt,
          });
        });
      }
    });

    // 计算汇总信息
    const summary = {
      mainOrderAmount: Number(order.totalFee),
      additionalServiceCount: additionalServices.length,
      additionalServiceAmount: additionalServices.reduce(
        (sum, service) => sum + service.totalFee,
        0
      ),
      totalRefundableAmount:
        Number(order.totalFee) +
        additionalServices.reduce((sum, service) => sum + service.totalFee, 0),
    };

    return {
      orderId: order.id,
      orderSn: order.sn,
      customer: order.customer,
      employee: order.employee,
      summary,
      mainOrder: mainOrderInfo,
      additionalServices: additionalServices.sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      ),
    };
  }

  @Post('/refund-audit', { summary: '统一退款审核（支持主订单和追加服务）' })
  async unifiedRefundAudit(
    @Body()
    body: {
      operatorId: number;
      result: boolean;
      reason?: string;
      refundItems: Array<{
        type: 'main_order' | 'additional_service';
        id: string | number; // 主订单使用sn，追加服务使用id
        refundAmount?: number;
        shouldRefundCoupons?: boolean;
      }>;
    }
  ) {
    if (!body.operatorId) {
      throw new CustomError('操作员ID不能为空', 400);
    }

    if (!body.refundItems || body.refundItems.length === 0) {
      throw new CustomError('退款项目不能为空', 400);
    }

    // 如果审核不通过，只需要记录原因，不执行实际退款
    if (!body.result) {
      if (!body.reason) {
        throw new CustomError('审核不通过时必须填写原因', 400);
      }

      // 这里可以记录审核不通过的日志
      this.ctx.logger.info('【退款审核不通过】：', {
        operatorId: body.operatorId,
        reason: body.reason,
        refundItems: body.refundItems,
      });

      return {
        success: true,
        message: '退款审核不通过',
        auditResult: false,
        reason: body.reason,
      };
    }

    // 审核通过，执行退款操作
    const results = [];
    let totalRefundAmount = 0;

    this.ctx.logger.info('【统一退款审核开始】：', {
      operatorId: body.operatorId,
      refundItems: body.refundItems,
      reason: body.reason,
    });

    // 使用数据库事务确保操作的原子性
    const transaction = await Order.sequelize.transaction();

    try {
      for (const item of body.refundItems) {
        this.ctx.logger.info(
          `【处理退款项目】类型: ${item.type}, ID: ${item.id}, 金额: ${item.refundAmount}`
        );
        try {
          if (item.type === 'main_order') {
            // 主订单退款
            await this.orderService.adminAuditRefund({
              sn: item.id as string,
              result: true,
              reason: body.reason,
              money: item.refundAmount,
              shouldRefundCoupons: item.shouldRefundCoupons !== false, // 默认退还卡券
              transaction, // 传递事务对象
            });

            results.push({
              type: 'main_order',
              id: item.id,
              success: true,
              refundAmount: item.refundAmount,
              message: '主订单退款成功',
            });

            totalRefundAmount += item.refundAmount || 0;
          } else if (item.type === 'additional_service') {
            // 追加服务退款
            const additionalServiceOrder =
              await AdditionalServiceOrder.findByPk(item.id as number, {
                transaction,
              });

            if (!additionalServiceOrder) {
              throw new CustomError(`追加服务订单不存在: ${item.id}`);
            }

            await this.additionalServiceOrderService.adminRefundAdditionalServiceOrder(
              item.id as number,
              body.reason || '管理员退款审核通过',
              item.shouldRefundCoupons !== false, // 默认退还卡券
              transaction // 传递事务对象
            );

            results.push({
              type: 'additional_service',
              id: item.id,
              success: true,
              refundAmount:
                item.refundAmount || additionalServiceOrder.totalFee,
              message: '追加服务退款成功',
            });

            totalRefundAmount +=
              item.refundAmount || Number(additionalServiceOrder.totalFee);
          }
        } catch (error) {
          this.ctx.logger.error(
            `退款项目处理失败: ${item.type}-${item.id}`,
            error
          );
          results.push({
            type: item.type,
            id: item.id,
            success: false,
            error: error.message,
            message: `${
              item.type === 'main_order' ? '主订单' : '追加服务'
            }退款失败`,
          });
          // 如果有任何失败，抛出异常以回滚事务
          throw error;
        }
      }

      // 所有操作成功，提交事务
      await transaction.commit();
    } catch (error) {
      // 有错误发生，回滚事务
      await transaction.rollback();
      this.ctx.logger.error('【统一退款审核失败，事务已回滚】：', {
        operatorId: body.operatorId,
        refundItems: body.refundItems,
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }

    // 记录操作日志
    this.ctx.logger.info('【统一退款审核完成】：', {
      operatorId: body.operatorId,
      auditResult: body.result,
      reason: body.reason,
      totalRefundAmount,
      results,
    });

    const successCount = results.filter(r => r.success).length;
    const failCount = results.filter(r => !r.success).length;

    return {
      success: failCount === 0,
      message: `退款审核完成，成功${successCount}项，失败${failCount}项`,
      auditResult: true,
      totalRefundAmount: Number(totalRefundAmount.toFixed(2)),
      results,
      operator: {
        id: body.operatorId,
      },
    };
  }
}
