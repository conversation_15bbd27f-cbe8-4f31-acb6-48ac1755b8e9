import { Controller, Get, Inject, Query } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { EmployeeService } from '../../service/employee.service';
import { Op } from 'sequelize';
import * as ExcelJS from 'exceljs';
import * as dayjs from 'dayjs';

@Controller('/admin/employees')
export class EmployeeAdminController {
  @Inject()
  ctx: Context;

  @Inject()
  employeeService: EmployeeService;

  @Get('/export', { summary: '管理端导出员工信息Excel' })
  async exportExcel(@Query() query: any) {
    // 移除分页参数，获取所有数据
    const { current, pageSize, ...exportQuery } = query;
    void current;
    void pageSize;

    const { name, phone, status, position, ...queryInfo } = exportQuery;

    // 构建查询条件
    if (name) {
      queryInfo.name = {
        [Op.like]: `%${name}%`,
      };
    }

    if (phone) {
      queryInfo.phone = {
        [Op.like]: `%${phone}%`,
      };
    }

    if (status !== undefined) {
      queryInfo.status = status;
    }

    if (position) {
      queryInfo.position = {
        [Op.like]: `%${position}%`,
      };
    }

    // 获取所有符合条件的员工数据
    const result = await this.employeeService.findAll({
      query: queryInfo,
      include: ['vehicle'],
      order: [['updatedAt', 'DESC']],
    });

    // 生成Excel
    return this.generateExcel(result.list);
  }

  private async generateExcel(employees: any[]) {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('员工列表');

    // 设置标题
    worksheet.mergeCells('A1:M1');
    const titleCell = worksheet.getCell('A1');
    titleCell.value = '员工信息导出列表';
    titleCell.font = { size: 16, bold: true };
    titleCell.alignment = { horizontal: 'center' };

    // 设置表头
    const headers = [
      '员工ID',
      '姓名',
      '手机号',
      '职位',
      '接单等级',
      '工作经验(月)',
      '服务评分',
      '钱包余额',
      '推广码',
      '入职时间',
      '离职时间',
      '状态',
      '车辆信息',
    ];

    const headerRow = worksheet.addRow(headers);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' },
    };

    // 添加数据行
    employees.forEach(employee => {
      const statusText = employee.status === 1 ? '在职' : '离职';
      const vehicleInfo = employee.vehicle
        ? `${employee.vehicle.plateNumber || ''}(${
            employee.vehicle.status || ''
          })`
        : '';

      worksheet.addRow([
        employee.id || '',
        employee.name || '',
        employee.phone || '',
        employee.position || '',
        employee.level || '',
        employee.workExp || '',
        employee.rating ? Number(employee.rating).toFixed(1) : '',
        employee.walletBalance
          ? Number(employee.walletBalance).toFixed(2)
          : '0.00',
        employee.promotionCode || '',
        employee.hireDate ? dayjs(employee.hireDate).format('YYYY-MM-DD') : '',
        employee.resignDate
          ? dayjs(employee.resignDate).format('YYYY-MM-DD')
          : '',
        statusText,
        vehicleInfo,
      ]);
    });

    // 自动调整列宽
    worksheet.columns.forEach(column => {
      column.width = 15;
    });

    // 设置响应头
    this.ctx.set({
      'Content-Type':
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'Content-Disposition': `attachment; filename=employees_${dayjs().format(
        'YYYYMMDD_HHmmss'
      )}.xlsx`,
    });

    // 返回Excel文件
    return await workbook.xlsx.writeBuffer();
  }
}
