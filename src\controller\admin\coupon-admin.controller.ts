import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { CouponService } from '../../service/coupon.service';
import { CustomError } from '../../error/custom.error';
import { Op } from 'sequelize';

@Controller('/admin/coupons')
export class CouponAdminController {
  @Inject()
  ctx: Context;

  @Inject()
  service: CouponService;

  @Get('/', { summary: '管理端查询代金券列表' })
  async index(
    @Query('current') current = 1,
    @Query('pageSize') pageSize = 10,
    @Query('isEnabled') isEnabled?: boolean,
    @Query('name') name?: string
  ) {
    const query: any = {};

    if (isEnabled !== undefined) {
      query.isEnabled = isEnabled;
    }

    if (name) {
      query.name = {
        [Op.like]: `%${name}%`,
      };
    }

    return this.service.findAll({
      query,
      offset: (current - 1) * pageSize,
      limit: pageSize,
      order: [['createdAt', 'DESC']],
    });
  }

  @Get('/:id', { summary: '管理端查询代金券详情' })
  async show(@Param('id') id: number) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定代金券', 404);
    }
    return res;
  }

  @Post('/', { summary: '管理端创建代金券' })
  async create(
    @Body()
    body: {
      name: string;
      description?: string;
      amount: number;
      minAmount?: number;
      price: number;
      validFrom?: Date;
      validTo?: Date;
      isEnabled: boolean;
      operatorId: number;
    }
  ) {
    const { operatorId, ...couponData } = body;

    if (!operatorId) {
      throw new CustomError('操作员ID不能为空');
    }

    this.ctx.logger.info('【管理端创建代金券】：', {
      operatorId,
      couponData,
    });

    const res = await this.service.create(couponData);

    this.ctx.logger.info('【管理端创建代金券成功】：', {
      couponId: res.id,
      operatorId,
    });

    return res;
  }

  @Put('/:id', { summary: '管理端更新代金券' })
  async update(
    @Param('id') id: number,
    @Body()
    body: {
      name?: string;
      description?: string;
      amount?: number;
      minAmount?: number;
      price?: number;
      validFrom?: Date;
      validTo?: Date;
      isEnabled?: boolean;
      operatorId: number;
    }
  ) {
    const { operatorId, ...updateData } = body;

    if (!operatorId) {
      throw new CustomError('操作员ID不能为空');
    }

    const coupon = await this.service.findById(id);
    if (!coupon) {
      throw new CustomError('代金券不存在', 404);
    }

    this.ctx.logger.info('【管理端更新代金券】：', {
      couponId: id,
      operatorId,
      updateData,
    });

    await this.service.update({ id }, updateData);

    this.ctx.logger.info('【管理端更新代金券成功】：', {
      couponId: id,
      operatorId,
    });

    return { success: true, message: '代金券更新成功' };
  }

  @Del('/:id', { summary: '管理端删除代金券' })
  async destroy(
    @Param('id') id: number,
    @Body()
    body: {
      operatorId: number;
      reason?: string;
    }
  ) {
    const { operatorId, reason } = body;

    if (!operatorId) {
      throw new CustomError('操作员ID不能为空');
    }

    const coupon = await this.service.findById(id);
    if (!coupon) {
      throw new CustomError('代金券不存在', 404);
    }

    this.ctx.logger.info('【管理端删除代金券】：', {
      couponId: id,
      operatorId,
      reason,
    });

    // TODO: 检查是否有用户持有该代金券
    // 这里可以添加业务逻辑检查

    await this.service.delete({ id });

    this.ctx.logger.info('【管理端删除代金券成功】：', {
      couponId: id,
      operatorId,
    });

    return {
      success: true,
      message: '代金券删除成功',
      operator: {
        id: operatorId,
      },
    };
  }
}
