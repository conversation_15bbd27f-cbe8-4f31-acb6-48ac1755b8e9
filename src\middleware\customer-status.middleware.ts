import { Middleware, IMiddleware, Logger, ILogger } from '@midwayjs/core';
import { NextFunction, Context } from '@midwayjs/koa';
import { Customer } from '../entity';
import { CustomError } from '../error/custom.error';

/**
 * 客户状态检查中间件
 * 检查客户是否被禁用，禁用的客户无法访问订单相关接口
 */
@Middleware()
export class CustomerStatusMiddleware
  implements IMiddleware<Context, NextFunction>
{
  @Logger()
  logger: ILogger;

  resolve() {
    return async (ctx: Context, next: NextFunction) => {
      // 只对已认证的customer用户进行状态检查
      if (!ctx.state.user || ctx.state.user.userType !== 'customer') {
        return await next();
      }

      // 检查是否是需要状态验证的路径
      if (!this.shouldCheckStatus(ctx)) {
        return await next();
      }

      try {
        const customerId = ctx.state.user.userId;
        const customer = await Customer.findByPk(customerId, {
          attributes: ['id', 'status'],
        });

        if (!customer) {
          throw new CustomError('用户不存在', 404);
        }

        // 检查用户是否被禁用 (status: 0-禁用 1-启用)
        if (customer.status === 0) {
          // 对于订单相关接口，返回空数据而不是错误
          if (this.isOrderRelatedPath(ctx)) {
            this.returnEmptyData(ctx);
            return;
          }
          throw new CustomError('账户已被禁用，请联系客服', 403);
        }
      } catch (error) {
        if (error instanceof CustomError) {
          throw error;
        }
        this.logger.error('检查客户状态失败:', error);
        throw new CustomError('用户状态验证失败', 500);
      }

      await next();
    };
  }

  /**
   * 判断是否需要检查用户状态
   * @param ctx 上下文
   */
  private shouldCheckStatus(ctx: Context): boolean {
    const path = ctx.path;

    // 需要检查状态的路径模式
    const checkPaths = [
      '/orders', // 订单相关
      '/membership-card-orders', // 权益卡订单
      '/coupon-orders', // 代金券订单
      '/customers/', // 客户相关操作（创建订单等）
      '/wepay', // 支付相关
    ];

    // 排除服务查询相关路径，允许禁用用户查看服务列表
    const excludePaths = [
      '/service', // 服务列表查询
      '/service-type', // 服务类型查询
      '/openapi/service', // 开放API服务查询
    ];

    // 如果是排除路径，不检查状态
    if (excludePaths.some(pattern => path.startsWith(pattern))) {
      return false;
    }

    return checkPaths.some(pattern => path.startsWith(pattern));
  }

  /**
   * 判断是否是订单相关路径
   * @param ctx 上下文
   */
  private isOrderRelatedPath(ctx: Context): boolean {
    const path = ctx.path;
    const method = ctx.method;

    // 对于GET请求的订单查询，返回空数据
    if (method === 'GET') {
      return (
        path.includes('/orders') ||
        path.includes('/membership-card-orders') ||
        path.includes('/coupon-orders')
      );
    }

    return false;
  }

  /**
   * 返回空数据响应
   * @param ctx 上下文
   */
  private returnEmptyData(ctx: Context): void {
    ctx.status = 200;
    ctx.body = {
      errCode: 0,
      msg: 'success',
      data: {
        list: [],
        total: 0,
        current: 1,
        pageSize: 10,
      },
    };
  }

  static getName(): string {
    return 'CUSTOMER_STATUS';
  }
}
