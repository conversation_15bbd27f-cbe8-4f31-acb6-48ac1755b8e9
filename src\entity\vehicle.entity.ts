import { Table, Column, Model, DataType, HasMany } from 'sequelize-typescript';
import { Employee } from './employee.entity';

export interface VehicleAttributes {
  /** 车辆ID */
  id: number;
  /** 车牌号 */
  plateNumber: string;
  /** 实时纬度 */
  latitude?: number;
  /** 实时经度 */
  longitude?: number;
  /** 车辆状态（空闲/服务中） */
  status: string;
  /** 里程数（公里） */
  mileage?: number;
  /** 外观描述 */
  appearance?: string;
  /** 保险到期时间 */
  insuranceExpiry?: Date;
  /** 行驶证到期时间 */
  licenseExpiry?: Date;
  /** 物资清单 */
  supplies?: string;
  /** 最后更新时间（员工端提交） */
  lastSubmittedAt?: Date;
  /** 最后提交人ID */
  lastSubmittedBy?: number;
  /** 关联的员工信息列表 */
  employees?: Employee[];
  /** 兼容性字段：主要员工信息 */
  employee?: Employee;
}

@Table({ tableName: 'vehicles', timestamps: true, comment: '车辆表' })
export class Vehicle
  extends Model<VehicleAttributes>
  implements VehicleAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '车辆ID',
  })
  id: number;

  @Column({
    type: DataType.STRING(20),
    unique: {
      name: 'plateNumber',
      msg: '已存在相同的车牌号',
    },
    allowNull: false,
    comment: '车牌号',
  })
  plateNumber: string;

  @Column({
    type: DataType.DECIMAL(9, 6),
    comment: '实时纬度',
  })
  latitude: number;

  @Column({
    type: DataType.DECIMAL(9, 6),
    comment: '实时经度',
  })
  longitude: number;

  @Column({
    type: DataType.STRING(20),
    defaultValue: '空闲',
    comment: '状态（空闲/服务中）',
  })
  status: string;

  @Column({
    type: DataType.DECIMAL(10, 2),
    comment: '里程数（公里）',
  })
  mileage: number;

  @Column({
    type: DataType.TEXT,
    comment: '外观描述',
  })
  appearance: string;

  @Column({
    type: DataType.DATE,
    comment: '保险到期时间',
  })
  insuranceExpiry: Date;

  @Column({
    type: DataType.DATE,
    comment: '行驶证到期时间',
  })
  licenseExpiry: Date;

  @Column({
    type: DataType.TEXT,
    comment: '物资清单',
  })
  supplies: string;

  @Column({
    type: DataType.DATE,
    comment: '最后更新时间（员工端提交）',
  })
  lastSubmittedAt: Date;

  @Column({
    type: DataType.INTEGER,
    comment: '最后提交人ID',
  })
  lastSubmittedBy: number;

  @HasMany(() => Employee)
  employees: Employee[];
}
