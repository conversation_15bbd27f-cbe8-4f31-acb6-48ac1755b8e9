import { Body, Controller, Inject, Post } from '@midwayjs/core';
import { Customer, CustomerAttributes } from '../../entity';
import { EmployeeService } from '../../service/employee.service';
import { JwtService } from '@midwayjs/jwt';
import { RegisterUserInfo } from '../../interface';
import { CustomError } from '../../error/custom.error';

@Controller('/openapi/user')
export class UserController {
  @Inject()
  jwtService: JwtService;

  @Inject()
  employeeService: EmployeeService;

  @Post('/register')
  async register(@Body() body: RegisterUserInfo) {
    const { openid, avatarUrl, nickName, phone, gender } = body;
    const userInfo: Omit<CustomerAttributes, 'id'> = {
      openid,
      phone,
      nickname: nickName,
      avatar: avatarUrl,
      gender,
      memberStatus: 0,
      points: 0,
      lastLoginTime: new Date(),
      status: 1,
      promotionCode: '', // 客户推广码由BeforeCreate钩子自动生成
    };
    const user = await Customer.create(userInfo);
    return user;
  }

  @Post('/login-for-employee', { summary: '员工登录' })
  async loginForEmployee(@Body('phone') phone: string) {
    const user = await this.employeeService.findByPhone(phone);
    if (!user) {
      return {
        code: 404,
        message: '用户不存在',
      };
    }

    // 检查员工状态
    if (user.status === 0) {
      throw new CustomError('员工已离职，无法登录', 403);
    }
    // 生成访问 token
    const accessPayload = {
      userId: user.id,
      type: 'access',
      userType: 'employee',
    };
    const token = this.jwtService.signSync(accessPayload, { expiresIn: '7d' });

    // TODO: 生成刷新 token，用于刷新访问 token，20 天过期，暂时未使用
    const refreshPayload = {
      userId: 'employee_' + user.id,
      type: 'refresh',
    };
    const refreshToken = this.jwtService.signSync(refreshPayload, {
      expiresIn: '30d',
    });

    return {
      token,
      refreshToken,
      user,
    };
  }
}
