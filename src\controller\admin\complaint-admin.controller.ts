import {
  Body,
  Controller,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Patch,
  Del,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { ComplaintService } from '../../service/complaint.service';
import { ComplaintOperationLogService } from '../../service/complaint-operation-log.service';
import { Op } from 'sequelize';
import { Customer, Order, Employee, User } from '../../entity';
import {
  AdminCreateComplaintDto,
  QueryComplaintDto,
  HandleComplaintDto,
  UpdateComplaintDto,
  BatchHandleComplaintDto,
  BatchDeleteComplaintDto,
} from '../../dto/complaint.dto';
import { CustomError } from '../../error/custom.error';

@Controller('/admin/complaints')
export class ComplaintAdminController {
  @Inject()
  ctx: Context;

  @Inject()
  service: ComplaintService;

  @Inject()
  complaintOperationLogService: ComplaintOperationLogService;

  @Get('/', { summary: '管理端查询所有投诉建议列表' })
  async index(@Query() query: QueryComplaintDto) {
    let { offset, limit } = query;
    const {
      offset: o,
      limit: l,
      current,
      pageSize,
      keyword,
      title,
      content,
      startDate,
      endDate,
      sourceType,
      ...queryInfo
    } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }

    // 构建查询条件
    const whereCondition: any = { ...queryInfo };

    // 处理来源类型筛选
    if (sourceType) {
      switch (sourceType) {
        case 'customer':
          whereCondition.customerId = { [Op.ne]: null };
          break;
        case 'employee':
          whereCondition.customerId = null;
          whereCondition.employeeId = { [Op.ne]: null };
          whereCondition.category = 'suggestion';
          break;
        case 'admin':
          whereCondition.createdBy = { [Op.ne]: null };
          break;
      }
    }

    // 处理日期范围查询
    if (startDate || endDate) {
      whereCondition.createdAt = {};
      if (startDate) {
        whereCondition.createdAt[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        whereCondition.createdAt[Op.lte] = new Date(endDate + ' 23:59:59');
      }
    }

    // 处理标题筛选
    if (title) {
      whereCondition.title = { [Op.like]: `%${title}%` };
    }

    // 处理内容筛选
    if (content) {
      whereCondition.content = { [Op.like]: `%${content}%` };
    }

    // 处理关键词搜索（标题和内容）
    if (keyword) {
      whereCondition[Op.or] = [
        { title: { [Op.like]: `%${keyword}%` } },
        { content: { [Op.like]: `%${keyword}%` } },
      ];
    }

    const result = await this.service.findAll({
      query: whereCondition,
      offset,
      limit,
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: Customer,
          attributes: ['id', 'nickname', 'phone', 'avatar'],
          required: false, // 左连接，允许没有关联客户的投诉
        },
        {
          model: Order,
          attributes: ['id', 'sn', 'status', 'serviceTime'],
          required: false,
        },
        {
          model: Employee,
          attributes: ['id', 'name', 'phone', 'avatar'],
          required: false,
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'nickname'],
          required: false, // 左连接，允许没有关联管理员的投诉
        },
      ],
    });

    // 为每个投诉建议添加来源标识
    const enhancedList = result.list.map(complaint => {
      const complaintData = complaint.toJSON();

      // 判断来源类型
      let sourceType = '';
      let sourceName = '';

      if (complaintData.createdBy && complaintData.creator) {
        // 管理员录入的投诉建议 - 优先判断，因为这是最准确的标识
        sourceType = 'admin';
        sourceName =
          complaintData.creator.nickname ||
          complaintData.creator.username ||
          '管理员';
      } else if (complaintData.customerId && complaintData.customer) {
        // 客户发出的投诉建议
        sourceType = 'customer';
        sourceName = complaintData.customer.nickname || '客户';
      } else if (
        complaintData.employeeId &&
        complaintData.employee &&
        !complaintData.customerId
      ) {
        // 员工发出的建议
        sourceType = 'employee';
        sourceName = complaintData.employee.name || '员工';
      } else {
        // 其他情况
        sourceType = 'unknown';
        sourceName = '未知来源';
      }

      return {
        ...complaintData,
        sourceType,
        sourceName,
        sourceInfo: {
          type: sourceType,
          name: sourceName,
          description:
            sourceType === 'customer'
              ? '客户投诉建议'
              : sourceType === 'employee'
              ? '员工建议'
              : sourceType === 'admin'
              ? '管理员录入'
              : '未知来源',
        },
      };
    });

    return {
      ...result,
      list: enhancedList,
    };
  }

  @Get('/:id', { summary: '管理端按ID查询投诉建议详情' })
  async show(@Param('id') id: number) {
    const res = await this.service.getComplaintWithRelations(id);
    if (!res) {
      throw new CustomError('未找到指定投诉建议');
    }
    return res;
  }

  @Post('/', { summary: '管理端录入投诉建议' })
  async create(@Body() createDto: AdminCreateComplaintDto) {
    // 从登录信息中获取管理员ID
    const currentUser = this.ctx.state.user;
    if (!currentUser || !currentUser.userId) {
      throw new CustomError('请先登录管理员账号', 401);
    }

    const createdBy = currentUser.userId;
    const complaintData = { ...createDto };
    delete complaintData.createdBy; // 移除前端可能传递的createdBy字段

    // 验证必填字段的业务逻辑
    if (complaintData.subCategory === 'order' && !complaintData.orderId) {
      throw new CustomError('订单投诉必须提供订单ID', 400);
    }

    if (complaintData.subCategory === 'employee' && !complaintData.employeeId) {
      throw new CustomError('人员投诉必须提供员工ID', 400);
    }

    return await this.service.adminCreateComplaint(createdBy, complaintData);
  }

  @Get('/statistics/summary', { summary: '管理端获取投诉建议统计摘要' })
  async getStatisticsSummary(@Query() query: any) {
    const { startDate, endDate, category, subCategory } = query;

    const basicStats = await this.service.getComplaintStatistics({
      startDate,
      endDate,
      category,
      subCategory,
    });

    // 添加来源统计
    const sourceStats = await this.service.getComplaintSourceStatistics({
      startDate,
      endDate,
      category,
      subCategory,
    });

    return {
      ...basicStats,
      sourceStats,
    };
  }

  @Get('/customer-complaints', { summary: '查询客户投诉建议' })
  async getCustomerComplaintsList(@Query() query: QueryComplaintDto) {
    let { offset, limit } = query;
    const {
      offset: o,
      limit: l,
      current,
      pageSize,
      keyword,
      startDate,
      endDate,
      ...queryInfo
    } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }

    // 构建查询条件 - 只查询客户投诉建议
    const whereCondition: any = {
      ...queryInfo,
      customerId: { [Op.ne]: null }, // 只查询有客户ID的记录
    };

    // 处理日期范围查询
    if (startDate || endDate) {
      whereCondition.createdAt = {};
      if (startDate) {
        whereCondition.createdAt[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        whereCondition.createdAt[Op.lte] = new Date(endDate + ' 23:59:59');
      }
    }

    // 处理关键词搜索（标题和内容）
    if (keyword) {
      whereCondition[Op.or] = [
        { title: { [Op.like]: `%${keyword}%` } },
        { content: { [Op.like]: `%${keyword}%` } },
      ];
    }

    const result = await this.service.findAll({
      query: whereCondition,
      offset,
      limit,
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: Customer,
          attributes: ['id', 'nickname', 'phone', 'avatar'],
          required: true, // 内连接，确保有客户信息
        },
        {
          model: Order,
          attributes: ['id', 'sn', 'status', 'serviceTime'],
          required: false,
        },
        {
          model: Employee,
          attributes: ['id', 'name', 'phone', 'avatar'],
          required: false,
        },
      ],
    });

    // 为每个投诉建议添加来源标识
    const enhancedList = result.list.map(complaint => {
      const complaintData = complaint.toJSON();
      return {
        ...complaintData,
        sourceType: 'customer',
        sourceName: complaintData.customer?.nickname || '客户',
        sourceInfo: {
          type: 'customer',
          name: complaintData.customer?.nickname || '客户',
          description: '客户投诉建议',
        },
      };
    });

    return {
      ...result,
      list: enhancedList,
    };
  }

  @Get('/employee-suggestions', { summary: '查询员工建议' })
  async getEmployeeSuggestionsList(@Query() query: QueryComplaintDto) {
    let { offset, limit } = query;
    const {
      offset: o,
      limit: l,
      current,
      pageSize,
      title,
      startDate,
      endDate,
      ...queryInfo
    } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }

    // 构建查询条件 - 只查询员工建议
    const whereCondition: any = {
      ...queryInfo,
      customerId: null, // 客户ID为空
      employeeId: { [Op.ne]: null }, // 员工ID不为空
      category: 'suggestion', // 只查询建议类型
    };

    // 处理日期范围查询
    if (startDate || endDate) {
      whereCondition.createdAt = {};
      if (startDate) {
        whereCondition.createdAt[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        whereCondition.createdAt[Op.lte] = new Date(endDate + ' 23:59:59');
      }
    }

    // 处理关键词搜索（标题和内容）
    if (title) {
      whereCondition.title = { [Op.like]: `%${title}%` };
    }

    const result = await this.service.findAll({
      query: whereCondition,
      offset,
      limit,
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: Employee,
          attributes: ['id', 'name', 'phone', 'avatar'],
          required: true, // 内连接，确保有员工信息
        },
      ],
    });

    // 为每个建议添加来源标识
    const enhancedList = result.list.map(complaint => {
      const complaintData = complaint.toJSON();
      return {
        ...complaintData,
        sourceType: 'employee',
        sourceName: complaintData.employee?.name || '员工',
        sourceInfo: {
          type: 'employee',
          name: complaintData.employee?.name || '员工',
          description: '员工建议',
        },
      };
    });

    return {
      ...result,
      list: enhancedList,
    };
  }

  @Get('/admin-created', { summary: '查询管理员录入的投诉建议' })
  async getAdminCreatedComplaints(@Query() query: any) {
    let { offset, limit } = query;
    const {
      offset: o,
      limit: l,
      current,
      pageSize,
      keyword,
      startDate,
      endDate,
      createdBy,
      ...queryInfo
    } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }

    // 构建查询条件 - 只查询管理员录入的投诉
    const whereCondition: any = {
      ...queryInfo,
      createdBy: { [Op.ne]: null }, // 只查询有录入人员的记录
    };

    // 如果指定了录入人员
    if (createdBy) {
      whereCondition.createdBy = createdBy;
    }

    // 处理日期范围查询
    if (startDate || endDate) {
      whereCondition.createdAt = {};
      if (startDate) {
        whereCondition.createdAt[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        whereCondition.createdAt[Op.lte] = new Date(endDate + ' 23:59:59');
      }
    }

    // 处理关键词搜索（标题和内容）
    if (keyword) {
      whereCondition[Op.or] = [
        { title: { [Op.like]: `%${keyword}%` } },
        { content: { [Op.like]: `%${keyword}%` } },
      ];
    }

    return this.service.findAll({
      query: whereCondition,
      offset,
      limit,
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: Customer,
          attributes: ['id', 'nickname', 'phone', 'avatar'],
          required: false,
        },
        {
          model: Order,
          attributes: ['id', 'sn', 'status', 'serviceTime'],
          required: false,
        },
        {
          model: Employee,
          attributes: ['id', 'name', 'phone', 'avatar'],
          required: false,
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'nickname'],
          required: false,
        },
      ],
    });
  }

  @Get('/customer/:customerId/complaints', {
    summary: '管理端查询指定客户的投诉建议',
  })
  async getCustomerComplaints(
    @Param('customerId') customerId: number,
    @Query('page') page = 1,
    @Query('pageSize') pageSize = 10
  ) {
    return await this.service.getCustomerComplaints(customerId, page, pageSize);
  }

  @Get('/order/:orderId/complaints', {
    summary: '管理端查询指定订单的投诉建议',
  })
  async getOrderComplaints(@Param('orderId') orderId: number) {
    const complaints = await this.service.findAll({
      query: { orderId },
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: Customer,
          attributes: ['id', 'nickname', 'phone', 'avatar'],
          required: false,
        },
        {
          model: Order,
          attributes: ['id', 'sn', 'status', 'serviceTime'],
        },
        {
          model: Employee,
          attributes: ['id', 'name', 'phone', 'avatar'],
          required: false,
        },
      ],
    });

    return complaints;
  }

  @Get('/employee/:employeeId/complaints', {
    summary: '管理端查询指定员工的投诉建议',
  })
  async getEmployeeComplaints(@Param('employeeId') employeeId: number) {
    const complaints = await this.service.findAll({
      query: { employeeId },
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: Customer,
          attributes: ['id', 'nickname', 'phone', 'avatar'],
          required: false,
        },
        {
          model: Order,
          attributes: ['id', 'sn', 'status', 'serviceTime'],
          required: false,
        },
        {
          model: Employee,
          attributes: ['id', 'name', 'phone', 'avatar'],
        },
      ],
    });

    return complaints;
  }

  @Patch('/:id/handle', { summary: '管理端处理投诉建议' })
  async handleComplaint(
    @Param('id') id: number,
    @Body() handleDto: HandleComplaintDto
  ) {
    // 从登录信息中获取处理人员ID
    const currentUser = this.ctx.state.user;
    if (!currentUser || !currentUser.userId) {
      throw new CustomError('请先登录管理员账号', 401);
    }

    const handlerId = currentUser.userId;
    const handleData = {
      ...handleDto,
      handlerId,
    };

    return await this.service.handleComplaint(id, handleData);
  }

  @Put('/:id', { summary: '管理端更新投诉建议' })
  async updateComplaint(
    @Param('id') id: number,
    @Body() updateDto: UpdateComplaintDto
  ) {
    // 管理员可以更新任何状态的投诉建议
    const complaint = await this.service.findById(id);
    if (!complaint) {
      throw new CustomError('投诉建议不存在', 404);
    }

    return await this.service.adminUpdateComplaint(id, updateDto);
  }

  @Del('/:id', { summary: '管理端删除投诉建议' })
  async deleteComplaint(@Param('id') id: number) {
    await this.service.adminDeleteComplaint(id);
    return { message: '删除成功' };
  }

  @Post('/batch-handle', { summary: '管理端批量处理投诉建议' })
  async batchHandleComplaints(@Body() batchData: BatchHandleComplaintDto) {
    const { ids, handleData } = batchData;

    if (!ids || ids.length === 0) {
      throw new CustomError('请选择要处理的投诉建议', 400);
    }

    // 从登录信息中获取处理人员ID
    const currentUser = this.ctx.state.user;
    if (!currentUser || !currentUser.userId) {
      throw new CustomError('请先登录管理员账号', 401);
    }

    const handlerId = currentUser.userId;
    const finalHandleData = {
      ...handleData,
      handlerId,
    };

    const results = [];
    for (const id of ids) {
      try {
        const result = await this.service.handleComplaint(id, finalHandleData);
        results.push({ id, success: true, data: result });
      } catch (error) {
        results.push({ id, success: false, error: error.message });
      }
    }

    return {
      message: '批量处理完成',
      results,
      successCount: results.filter(r => r.success).length,
      failCount: results.filter(r => !r.success).length,
    };
  }

  @Del('/batch', { summary: '管理端批量删除投诉建议' })
  async batchDeleteComplaints(@Body() data: BatchDeleteComplaintDto) {
    const { ids } = data;

    if (!ids || ids.length === 0) {
      throw new CustomError('请选择要删除的投诉建议', 400);
    }

    const results = [];
    for (const id of ids) {
      try {
        await this.service.adminDeleteComplaint(id);
        results.push({ id, success: true });
      } catch (error) {
        results.push({ id, success: false, error: error.message });
      }
    }

    return {
      message: '批量删除完成',
      results,
      successCount: results.filter(r => r.success).length,
      failCount: results.filter(r => !r.success).length,
    };
  }

  @Patch('/:id/status', { summary: '管理端更新投诉建议状态' })
  async updateComplaintStatus(
    @Param('id') id: number,
    @Body()
    statusData: {
      status: 'pending' | 'processing' | 'resolved' | 'closed';
    }
  ) {
    const complaint = await this.service.findById(id);
    if (!complaint) {
      throw new CustomError('投诉建议不存在', 404);
    }

    // 获取当前用户信息
    const currentUser = this.ctx.state.user;
    const operatorId = currentUser?.userId;

    // 记录原始状态
    const originalStatus = complaint.status;

    // 简单的状态更新，不需要处理结果和处理人员
    await complaint.update({
      status: statusData.status,
      handledAt: statusData.status !== 'pending' ? new Date() : null,
    });

    // 记录操作日志
    await this.complaintOperationLogService.createOperationLog({
      complaintId: complaint.id,
      operationType: 'status_change',
      operatorType: 'admin',
      operatorId,
      beforeStatus: originalStatus,
      afterStatus: statusData.status,
      description: `管理员快速更新状态，从"${originalStatus}"变更为"${statusData.status}"`,
      operationDetails: {
        statusChange: {
          from: originalStatus,
          to: statusData.status,
        },
      },
    });

    return await this.service.getComplaintWithRelations(id);
  }

  @Get('/:id/history', { summary: '管理端查询投诉建议处理历史' })
  async getComplaintHistory(
    @Param('id') id: number,
    @Query('page') page = 1,
    @Query('pageSize') pageSize = 20
  ) {
    // 验证投诉建议是否存在
    const complaint = await this.service.getComplaintWithRelations(id);
    if (!complaint) {
      throw new CustomError('投诉建议不存在', 404);
    }

    // 获取操作历史记录
    const historyResult =
      await this.complaintOperationLogService.getComplaintOperationHistory(
        id,
        page,
        pageSize
      );

    return {
      complaint,
      history: historyResult,
    };
  }
}
