# 管理端权益卡订单相关API接口

## 权益卡订单状态枚举
```typescript
enum MembershipCardOrderStatus {
  待付款 = 'pending_payment',
  已付款 = 'paid',
  已取消 = 'cancelled',
  已退款 = 'refunded'
}
```

## 1. 权益卡订单查询接口

### 1.1 管理端查询所有权益卡订单列表
- **接口**: `GET /membership-card-orders/all`
- **描述**: 管理端查询所有权益卡订单列表，支持分页和状态筛选
- **参数**:
  - `current` (number): 页码，默认1
  - `pageSize` (number): 每页数量，默认10
  - `status` (string): 订单状态筛选，多个状态用逗号分隔
- **返回**: 包含订单列表、客户信息、权益卡类型信息

### 1.2 管理端查询权益卡订单详情
- **接口**: `GET /membership-card-orders/{id}`
- **描述**: 管理端查询指定权益卡订单的详细信息
- **参数**: `id` (number): 订单ID
- **返回**: 包含订单详情、客户信息、权益卡类型信息

### 1.3 管理端查询用户权益卡列表
- **接口**: `GET /customer-membership-cards`
- **描述**: 管理端查询用户权益卡列表，支持分页和筛选
- **参数**:
  - `current` (number): 页码，默认1
  - `pageSize` (number): 每页数量，默认10
  - `customerId` (number): 用户ID筛选
  - `status` (string): 权益卡状态筛选
  - `cardTypeId` (number): 权益卡类型ID筛选
- **返回**: 包含用户权益卡列表、客户信息、权益卡类型信息

### 1.4 管理端查询用户权益卡详情
- **接口**: `GET /customer-membership-cards/{id}`
- **描述**: 管理端查询指定用户权益卡的详细信息
- **参数**: `id` (number): 用户权益卡ID
- **返回**: 包含用户权益卡详情、客户信息、权益卡类型信息

## 2. 权益卡订单管理接口

### 2.1 管理端创建权益卡订单
- **接口**: `POST /membership-card-orders`
- **描述**: 管理端创建权益卡订单
- **状态**: ✅ 已实现（复用普通接口）
- **请求体**:
  ```typescript
  {
    customerId: number;    // 用户ID（必填）
    cardTypeId: number;    // 权益卡类型ID（必填）
    remark?: string;       // 备注（可选）
  }
  ```
- **返回**: 创建的订单信息

### 2.2 管理端支付权益卡订单
- **接口**: `POST /membership-card-orders/{sn}/pay`
- **描述**: 管理端支付权益卡订单
- **状态**: ✅ 已实现（复用普通接口）
- **参数**: `sn` (string): 订单编号
- **请求体**:
  ```typescript
  {
    customerId: number;    // 用户ID（必填）
  }
  ```
- **业务逻辑**:
  1. 验证订单状态为"待付款"
  2. 验证订单归属
  3. 更新订单状态为"已付款"
  4. 创建用户权益卡记录
  5. 更新用户会员状态为权益会员

### 2.3 管理端取消权益卡订单
- **接口**: `PUT /membership-card-orders/{sn}/cancel`
- **描述**: 管理端取消权益卡订单
- **状态**: ✅ 已实现（复用普通接口）
- **参数**: `sn` (string): 订单编号
- **请求体**:
  ```typescript
  {
    customerId: number;    // 用户ID（必填）
  }
  ```
- **限制**: 只有"待付款"状态的订单可以取消

### 2.4 管理端退款权益卡订单 ⚠️ 待实现
- **接口**: `POST /admin/membership-card-orders/{id}/refund`
- **描述**: 管理端处理权益卡订单退款（任何状态）
- **状态**: ❌ 未实现（需要创建专门的管理端控制器）
- **参数**: `id` (number): 订单ID
- **请求体**:
  ```typescript
  {
    operatorId: number;    // 操作员ID（必填）
    reason?: string;       // 退款原因（可选）
  }
  ```
- **业务逻辑**:
  1. 验证操作员权限
  2. 执行退款操作
  3. 更新订单状态为"已退款"
  4. 禁用相关的用户权益卡
  5. 检查用户会员状态并更新

### 2.5 管理端删除权益卡订单 ⚠️ 待实现
- **接口**: `DELETE /admin/membership-card-orders/{id}`
- **描述**: 管理端删除权益卡订单（任何状态）
- **状态**: ❌ 未实现（需要创建专门的管理端控制器）
- **参数**: `id` (number): 订单ID
- **请求体**:
  ```typescript
  {
    operatorId: number;    // 操作员ID（必填）
    reason?: string;       // 删除原因（可选）
  }
  ```
- **业务逻辑**:
  1. 验证操作员权限
  2. 检查订单支付状态
  3. 如果已支付，执行退款操作
  4. 禁用相关的用户权益卡
  5. 删除订单记录

## 3. 用户权益卡管理接口

### 3.1 管理端手动发放权益卡
- **接口**: `POST /customer-membership-cards`
- **描述**: 管理端手动为用户发放权益卡
- **请求体**:
  ```typescript
  {
    customerId: number;       // 用户ID（必填）
    cardTypeId: number;       // 权益卡类型ID（必填）
    purchaseTime?: Date;      // 购买时间（可选，默认当前时间）
    expiryTime?: Date;        // 过期时间（可选）
    remainTimes?: number;     // 剩余次数（可选）
    status: string;           // 状态（必填，默认'active'）
    operatorId: number;       // 操作员ID（必填）
    description?: string;     // 描述（可选）
  }
  ```
- **返回**: 创建的用户权益卡信息

### 3.2 管理端更新用户权益卡
- **接口**: `PUT /customer-membership-cards/{id}`
- **描述**: 管理端更新用户权益卡信息
- **参数**: `id` (number): 用户权益卡ID
- **请求体**:
  ```typescript
  {
    expiryTime?: Date;        // 过期时间（可选）
    remainTimes?: number;     // 剩余次数（可选）
    status?: string;          // 状态（可选）
    operatorId: number;       // 操作员ID（必填）
    description?: string;     // 描述（可选）
  }
  ```
- **返回**: 更新结果

### 3.3 管理端禁用用户权益卡
- **接口**: `POST /customer-membership-cards/{id}/disable`
- **描述**: 管理端禁用用户权益卡
- **参数**: `id` (number): 用户权益卡ID
- **请求体**:
  ```typescript
  {
    operatorId: number;       // 操作员ID（必填）
    description?: string;     // 描述（可选）
  }
  ```
- **业务逻辑**:
  1. 更新权益卡状态为'expired'
  2. 记录变更日志
  3. 检查用户会员状态并更新

### 3.4 管理端撤销用户权益卡
- **接口**: `POST /customer-membership-cards/{id}/revoke`
- **描述**: 管理端撤销用户权益卡
- **参数**: `id` (number): 用户权益卡ID
- **请求体**:
  ```typescript
  {
    operatorId: number;       // 操作员ID（必填）
    description?: string;     // 描述（可选）
  }
  ```
- **业务逻辑**:
  1. 更新权益卡状态为'expired'
  2. 将剩余次数设置为0
  3. 记录变更日志
  4. 检查用户会员状态并更新

## 4. 微信支付管理接口

### 4.1 管理端查询微信支付状态
- **接口**: `GET /admin/wepay/transactions/sn/{sn}`
- **描述**: 管理端查询权益卡订单的微信支付状态并同步本地状态
- **参数**: `sn` (string): 订单编号
- **功能**: 查询微信支付状态并同步到本地数据库
- **返回**:
  ```typescript
  {
    // 微信支付返回的原始数据
    ...wechatResult,
    // 本地同步结果
    localSyncResult: {
      synced: boolean;        // 是否同步成功
      message: string;        // 同步结果消息
    }
  }
  ```

## 5. 权益卡类型管理接口

### 5.1 管理端查询权益卡类型列表
- **接口**: `GET /admin/membership-card-types`
- **描述**: 管理端查询权益卡类型列表
- **参数**:
  - `current` (number): 页码，默认1
  - `pageSize` (number): 每页数量，默认10
  - `isEnabled` (boolean): 是否启用筛选
- **返回**: 权益卡类型列表

### 5.2 管理端创建权益卡类型
- **接口**: `POST /admin/membership-card-types`
- **描述**: 管理端创建权益卡类型
- **请求体**:
  ```typescript
  {
    name: string;             // 名称（必填）
    description?: string;     // 描述（可选）
    price: number;            // 价格（必填）
    discountRate?: number;    // 折扣率（折扣卡必填）
    usageLimit?: number;      // 使用次数限制（次卡必填）
    validDays?: number;       // 有效天数（可选）
    isEnabled: boolean;       // 是否启用（必填）
    operatorId: number;       // 操作员ID（必填）
  }
  ```
- **返回**: 创建的权益卡类型信息

### 5.3 管理端更新权益卡类型
- **接口**: `PUT /admin/membership-card-types/{id}`
- **描述**: 管理端更新权益卡类型
- **参数**: `id` (number): 权益卡类型ID
- **请求体**:
  ```typescript
  {
    name?: string;            // 名称（可选）
    description?: string;     // 描述（可选）
    price?: number;           // 价格（可选）
    discountRate?: number;    // 折扣率（可选）
    usageLimit?: number;      // 使用次数限制（可选）
    validDays?: number;       // 有效天数（可选）
    isEnabled?: boolean;      // 是否启用（可选）
    operatorId: number;       // 操作员ID（必填）
  }
  ```
- **返回**: 更新结果
