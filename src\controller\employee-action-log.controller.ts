import { Controller, Get, Query, Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { EmployeeActionLogService } from '../service/employee-action-log.service';

@Controller('/employee-action-logs')
export class EmployeeActionLogController {
  @Inject()
  ctx: Context;

  @Inject()
  employeeActionLogService: EmployeeActionLogService;

  /**
   * 获取员工动作记录列表
   */
  @Get('/list')
  async getEmployeeActionLogs(
    @Query()
    query: {
      startDate?: string;
      endDate?: string;
      employeeId?: number;
      orderSn?: string;
      changeType?: string;
      page?: number;
      pageSize?: number;
    }
  ) {
    return await this.employeeActionLogService.getEmployeeActionLogs(query);
  }

  /**
   * 获取员工动作统计概览
   */
  @Get('/overview')
  async getEmployeeActionOverview(
    @Query()
    query: {
      startDate?: string;
      endDate?: string;
      employeeId?: number;
    }
  ) {
    return await this.employeeActionLogService.getEmployeeActionOverview(query);
  }

  /**
   * 按员工统计动作记录
   */
  @Get('/stats-by-employee')
  async getActionStatsByEmployee(
    @Query()
    query: {
      startDate?: string;
      endDate?: string;
      page?: number;
      pageSize?: number;
    }
  ) {
    return await this.employeeActionLogService.getActionStatsByEmployee(query);
  }

  /**
   * 获取员工动作时间线
   */
  @Get('/timeline')
  async getEmployeeActionTimeline(
    @Query()
    query: {
      employeeId: number;
      startDate?: string;
      endDate?: string;
      orderId?: number;
    }
  ) {
    if (!query.employeeId) {
      return {
        errCode: 400,
        msg: '员工ID不能为空',
      };
    }

    return await this.employeeActionLogService.getEmployeeActionTimeline(query);
  }
}
