import { Provide } from '@midwayjs/core';
import { MembershipCardUsageRecord } from '../entity/membership-card-usage-record.entity';
import { BaseService } from '../common/BaseService';

@Provide()
export class MembershipCardUsageRecordService extends BaseService<MembershipCardUsageRecord> {
  constructor() {
    super('权益卡使用记录');
  }

  getModel() {
    return MembershipCardUsageRecord;
  }

  /**
   * 创建权益卡使用记录
   * @param customerMembershipCardId 客户权益卡ID
   * @param orderId 订单ID
   */
  async createUsageRecord(customerMembershipCardId: number, orderId: number) {
    return await this.create({
      customerMembershipCardId,
      orderId,
      useTime: new Date(),
      isRefunded: false,
    });
  }

  /**
   * 获取权益卡的使用记录
   * @param customerMembershipCardId 客户权益卡ID
   */
  async getUsageRecords(customerMembershipCardId: number) {
    return await this.findAll({
      query: { customerMembershipCardId },
      order: [['useTime', 'DESC']],
      include: ['order'],
    });
  }

  /**
   * 获取订单使用的权益卡记录
   * @param orderId 订单ID
   */
  async getOrderCardUsage(orderId: number) {
    return await this.findAll({
      query: { orderId },
      include: ['customerMembershipCard'],
    });
  }

  /**
   * 标记权益卡使用记录为已退回
   * @param orderId 订单ID
   * @param transaction 可选的事务对象
   */
  async markAsRefunded(orderId: number, transaction?: any) {
    const records = await this.findAll({
      query: { orderId, isRefunded: false },
    });

    if (records && records.list && records.list.length > 0) {
      await Promise.all(
        records.list.map(async (record: MembershipCardUsageRecord) => {
          await record.update({
            isRefunded: true,
            refundTime: new Date(),
          });
        })
      );
      return records.list.length;
    }
    return 0;
  }

  /**
   * 检查订单是否已退回权益卡
   * @param orderId 订单ID
   */
  async checkOrderRefundStatus(orderId: number) {
    const records = await this.findAll({
      query: { orderId },
    });

    if (records && records.list && records.list.length > 0) {
      // 检查是否所有记录都已退回
      const allRefunded = records.list.every(
        (record: MembershipCardUsageRecord) => record.isRefunded
      );
      return {
        hasUsageRecords: true,
        allRefunded,
        recordCount: records.list.length,
      };
    }
    return {
      hasUsageRecords: false,
      allRefunded: false,
      recordCount: 0,
    };
  }
}
