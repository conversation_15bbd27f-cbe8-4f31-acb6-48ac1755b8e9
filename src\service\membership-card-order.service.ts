import { Provide, Inject } from '@midwayjs/core';
import { BaseService } from '../common/BaseService';
import {
  MembershipCardOrder,
  MembershipCardOrderStatus,
} from '../entity/membership-card-order.entity';
import { CustomerMembershipCardService } from './customer-membership-card.service';
import { MembershipCardType } from '../entity/membership-card-type.entity';
import { Customer } from '../entity/customer.entity';
import { CustomError } from '../error/custom.error';
import { Op } from 'sequelize';
import { CustomerService } from './customer.service';

@Provide()
export class MembershipCardOrderService extends BaseService<MembershipCardOrder> {
  @Inject()
  customerMembershipCardService: CustomerMembershipCardService;

  @Inject()
  customerService: CustomerService;

  constructor() {
    super('权益卡订单');
  }

  getModel() {
    return MembershipCardOrder;
  }

  /**
   * 创建权益卡订单
   * @param customerId 用户ID
   * @param cardTypeId 权益卡类型ID
   * @param remark 备注
   */
  async createOrder(customerId: number, cardTypeId: number, remark?: string) {
    // 查询用户信息
    const customer = await Customer.findByPk(customerId);
    if (!customer) {
      throw new CustomError('用户不存在');
    }

    // 查询权益卡类型信息
    const cardType = await MembershipCardType.findByPk(cardTypeId);
    if (!cardType) {
      throw new CustomError('权益卡类型不存在');
    }

    // 检查权益卡类型是否启用
    if (!cardType.isEnabled) {
      throw new CustomError('该权益卡类型已停用');
    }

    // 生成订单编号：当前时间戳 + 4位随机数
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 10000)
      .toString()
      .padStart(4, '0');
    const sn = `MC${timestamp}${random}`;

    // 创建订单
    const order = await this.create({
      sn,
      customerId,
      cardTypeId,
      amount: cardType.price,
      status: MembershipCardOrderStatus.PENDING_PAYMENT,
      remark,
    });

    return order;
  }

  /**
   * 支付权益卡订单
   * @param customerId 用户ID
   * @param sn 订单编号
   */
  async payOrder(customerId: number, sn: string) {
    // 查询订单信息
    const order = await this.findOne({ where: { sn } });
    if (!order) {
      throw new CustomError('订单不存在');
    }

    // 检查订单是否属于该用户
    if (order.customerId !== customerId) {
      throw new CustomError('订单不属于该用户');
    }

    // 检查订单状态
    if (order.status !== MembershipCardOrderStatus.PENDING_PAYMENT) {
      throw new CustomError('订单状态不正确');
    }

    // 查询权益卡类型信息
    const cardType = await MembershipCardType.findByPk(order.cardTypeId);
    if (!cardType) {
      throw new CustomError('权益卡类型不存在');
    }

    // 更新订单状态
    await order.update({
      status: MembershipCardOrderStatus.PAID,
      payTime: new Date(),
    });

    // 创建用户权益卡
    const now = new Date();
    let expiryTime = null;
    if (cardType.validDays) {
      expiryTime = new Date(
        now.getTime() + cardType.validDays * 24 * 60 * 60 * 1000
      );
    }

    // 设置剩余次数
    let remainTimes = -1; // 默认不限次数
    if (cardType.usageLimit !== null && cardType.usageLimit !== undefined) {
      remainTimes = cardType.usageLimit;
    }

    const card = await this.customerMembershipCardService.createCard({
      customerId,
      cardTypeId: order.cardTypeId,
      purchaseTime: now,
      expiryTime,
      remainTimes,
      status: 'active',
    });

    // 更新用户会员状态为权益会员
    await this.customerService.updateMemberStatus(customerId, 1);

    return {
      order,
      card,
    };
  }

  /**
   * 取消权益卡订单
   * @param customerId 用户ID
   * @param sn 订单编号
   */
  async cancelOrder(customerId: number, sn: string) {
    // 查询订单信息
    const order = await this.findOne({ where: { sn } });
    if (!order) {
      throw new CustomError('订单不存在');
    }

    // 检查订单是否属于该用户
    if (order.customerId !== customerId) {
      throw new CustomError('订单不属于该用户');
    }

    // 检查订单状态
    if (order.status !== MembershipCardOrderStatus.PENDING_PAYMENT) {
      throw new CustomError('订单状态不正确');
    }

    // 更新订单状态
    await order.update({
      status: MembershipCardOrderStatus.CANCELLED,
      cancelTime: new Date(),
    });

    return order;
  }

  /**
   * 查询用户的权益卡订单列表
   * @param customerId 用户ID
   * @param status 订单状态
   * @param page 页码
   * @param pageSize 每页数量
   */
  async findCustomerOrders(
    customerId: number,
    status?: MembershipCardOrderStatus[],
    page = 1,
    pageSize = 10
  ) {
    const query: any = { customerId };
    if (status && status.length > 0) {
      query.status = { [Op.in]: status };
    }

    return await this.findAll({
      query,
      offset: (page - 1) * pageSize,
      limit: pageSize,
      order: [['updatedAt', 'DESC']],
      include: ['cardType'],
    });
  }

  /**
   * 管理员退款权益卡订单
   * @param id 订单ID
   * @param operatorId 操作员ID
   * @param reason 退款原因
   */
  async adminRefundOrder(id: number, operatorId: number, reason?: string) {
    const order = await this.findById(id);
    if (!order) {
      throw new CustomError('权益卡订单不存在');
    }

    // 管理员退款无需状态检查，可以对任何状态的订单操作
    await order.update({
      status: MembershipCardOrderStatus.REFUNDED,
      refundTime: new Date(),
    });

    return order;
  }

  /**
   * 管理员删除权益卡订单
   * @param id 订单ID
   * @param operatorId 操作员ID
   * @param reason 删除原因
   */
  async adminDeleteOrder(id: number, operatorId: number, reason?: string) {
    const order = await this.findById(id);
    if (!order) {
      throw new CustomError('权益卡订单不存在');
    }

    await order.destroy();
    return true;
  }

  /**
   * 获取权益卡订单统计
   * @param query 查询条件
   */
  async getStatistics(query: any = {}) {
    const totalOrders = await MembershipCardOrder.count({ where: query });
    const paidOrders = await MembershipCardOrder.count({
      where: { ...query, status: MembershipCardOrderStatus.PAID },
    });
    const pendingOrders = await MembershipCardOrder.count({
      where: { ...query, status: MembershipCardOrderStatus.PENDING_PAYMENT },
    });
    const cancelledOrders = await MembershipCardOrder.count({
      where: { ...query, status: MembershipCardOrderStatus.CANCELLED },
    });
    const refundedOrders = await MembershipCardOrder.count({
      where: { ...query, status: MembershipCardOrderStatus.REFUNDED },
    });

    // 计算总金额
    const totalAmount =
      (await MembershipCardOrder.sum('amount', { where: query })) || 0;
    const paidAmount =
      (await MembershipCardOrder.sum('amount', {
        where: { ...query, status: MembershipCardOrderStatus.PAID },
      })) || 0;

    return {
      totalOrders,
      paidOrders,
      pendingOrders,
      cancelledOrders,
      refundedOrders,
      totalAmount: Number(totalAmount).toFixed(2),
      paidAmount: Number(paidAmount).toFixed(2),
    };
  }
}
