import { Provide } from '@midwayjs/core';
import {
  CouponUsageRecord,
  CouponUsageRecordAttributes,
} from '../entity/coupon-usage-record.entity';
import { BaseService } from '../common/BaseService';

@Provide()
export class CouponUsageRecordService extends BaseService<CouponUsageRecord> {
  constructor() {
    super('代金券使用记录');
  }

  getModel() {
    return CouponUsageRecord;
  }

  /**
   * 创建代金券使用记录
   * @param customerCouponId 客户代金券ID
   * @param orderId 订单ID
   */
  async createUsageRecord(customerCouponId: number, orderId: number) {
    return await this.create({
      customerCouponId,
      orderId,
      useTime: new Date(),
    });
  }

  /**
   * 获取代金券的使用记录
   * @param customerCouponId 客户代金券ID
   */
  async getUsageRecords(customerCouponId: number) {
    return await this.findAll({
      query: { customerCouponId },
      order: [['useTime', 'DESC']],
      include: ['order'],
    });
  }

  /**
   * 获取订单使用的代金券记录
   * @param orderId 订单ID
   */
  async getOrderCouponUsage(orderId: number) {
    return await this.findAll({
      query: { orderId },
      include: ['customerCoupon'],
    });
  }

  /**
   * 标记权益卡使用记录为已退回
   * @param orderId 订单ID
   * @param transaction 可选的事务对象
   */
  async markAsRefunded(orderId: number, transaction?: any) {
    const records = await this.findAll({
      query: { orderId, isRefunded: false },
    });

    if (records && records.list && records.list.length > 0) {
      await Promise.all(
        records.list.map(async (record: CouponUsageRecord) => {
          await record.update({
            isRefunded: true,
            refundTime: new Date(),
          }, { transaction });
        })
      );
      return records.list.length;
    }
    return 0;
  }

  /**
   * 检查订单是否已退回权益卡
   * @param orderId 订单ID
   */
  async checkOrderRefundStatus(orderId: number) {
    const records = await this.findAll({
      query: { orderId },
    });

    if (records && records.list && records.list.length > 0) {
      // 检查是否所有记录都已退回
      const allRefunded = records.list.every(
        (record: CouponUsageRecordAttributes) => record.isRefunded
      );
      return {
        hasUsageRecords: true,
        allRefunded,
        recordCount: records.list.length,
      };
    }
    return {
      hasUsageRecords: false,
      allRefunded: false,
      recordCount: 0,
    };
  }
}
