import { Controller, Get, Inject, Query } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { CustomerService } from '../../service/customer.service';
import { Op } from 'sequelize';
import * as ExcelJS from 'exceljs';
import * as dayjs from 'dayjs';

@Controller('/admin/customers')
export class CustomerAdminController {
  @Inject()
  ctx: Context;

  @Inject()
  customerService: CustomerService;

  @Get('/export', { summary: '管理端导出用户信息Excel' })
  async exportExcel(@Query() query: any) {
    // 移除分页参数，获取所有数据
    const { current, pageSize, ...exportQuery } = query;
    void current;
    void pageSize;

    const { phone, nickname, memberStatus, status, ...queryInfo } = exportQuery;

    // 构建查询条件
    if (phone) {
      queryInfo.phone = {
        [Op.like]: `%${phone}%`,
      };
    }

    if (nickname) {
      queryInfo.nickname = {
        [Op.like]: `%${nickname}%`,
      };
    }

    if (memberStatus !== undefined) {
      queryInfo.memberStatus = memberStatus;
    }

    if (status !== undefined) {
      queryInfo.status = status;
    }

    // 获取所有符合条件的用户数据
    const result = await this.customerService.findAll({
      query: queryInfo,
      include: ['promotionEmployee', 'addresses'],
      order: [['updatedAt', 'DESC']],
    });

    // 生成Excel
    return this.generateExcel(result.list);
  }

  private async generateExcel(customers: any[]) {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('用户列表');

    // 设置标题
    worksheet.mergeCells('A1:N1');
    const titleCell = worksheet.getCell('A1');
    titleCell.value = '用户信息导出列表';
    titleCell.font = { size: 16, bold: true };
    titleCell.alignment = { horizontal: 'center' };

    // 设置表头
    const headers = [
      '用户ID',
      '昵称',
      '手机号',
      '性别',
      '详细地址',
      '收货地址',
      '会员状态',
      '积分值',
      '推广码',
      '推广员工',
      '注册时间',
      '状态',
    ];

    const headerRow = worksheet.addRow(headers);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' },
    };

    // 添加数据行
    customers.forEach(customer => {
      const genderText =
        customer.gender === 1 ? '男' : customer.gender === 2 ? '女' : '未知';
      const memberStatusText =
        customer.memberStatus === 1 ? '权益会员' : '普通会员';
      const statusText = customer.status === 1 ? '启用' : '禁用';
      const promotionEmployeeName = customer.promotionEmployee?.name || '';

      // 获取默认收货地址或第一个地址
      const defaultAddress =
        customer.addresses?.find((addr: any) => addr.isDefault) ||
        customer.addresses?.[0];
      const addressInfo = defaultAddress
        ? `${defaultAddress.provinceName}${defaultAddress.cityName}${defaultAddress.districtName} ${defaultAddress.detailAddress}`
        : '';

      worksheet.addRow([
        customer.id || '',
        customer.nickname || '',
        customer.phone || '',
        genderText,
        customer.address || '',
        addressInfo,
        memberStatusText,
        customer.points || 0,
        customer.promotionCode || '',
        promotionEmployeeName,
        customer.createdAt
          ? dayjs(customer.createdAt).format('YYYY-MM-DD HH:mm:ss')
          : '',
        statusText,
      ]);
    });

    // 自动调整列宽
    worksheet.columns.forEach(column => {
      column.width = 15;
    });

    // 设置响应头
    this.ctx.set({
      'Content-Type':
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'Content-Disposition': `attachment; filename=customers_${dayjs().format(
        'YYYYMMDD_HHmmss'
      )}.xlsx`,
    });

    // 返回Excel文件
    return await workbook.xlsx.writeBuffer();
  }
}
