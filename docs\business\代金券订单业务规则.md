# 代金券订单业务规则

## 代金券订单状态流转

### 基本状态流转
```
待付款 → 已付款
```

### 异常状态流转
```
待付款 → 已取消
已付款 → 已退款
```

### 状态说明
- **待付款**: 订单创建后，等待用户付款
- **已付款**: 付款完成，代金券已发放给用户
- **已取消**: 订单被取消（仅限待付款状态）
- **已退款**: 订单已退款，代金券被回收

## 代金券订单创建规则

### 基本信息验证
1. **客户信息**: 必须提供有效的客户ID
2. **代金券信息**: 必须选择有效且启用的代金券
3. **代金券状态**: 代金券必须处于启用状态
4. **订单编号**: 自动生成，格式为 CP + 时间戳 + 4位随机数

### 代金券类型规则
1. **面额设置**: 代金券必须设置面额（amount字段）
2. **使用条件**: 可以设置最低消费金额（minAmount字段）
3. **有效期**: 可以设置有效期（validFrom和validTo字段）
4. **适用范围**: 可以设置适用的服务类型或特定服务

### 价格计算规则
1. **订单金额**: 直接使用代金券的销售价格
2. **无优惠**: 代金券订单本身不支持使用其他优惠
3. **固定价格**: 每个代金券有固定的销售价格

## 代金券发放规则

### 支付成功后自动发放
1. **代金券创建**: 支付成功后自动创建用户代金券记录
2. **有效期设置**: 
   - 如果代金券设置了有效期，使用代金券的有效期
   - 未设置有效期的代金券永久有效
3. **状态设置**: 新创建的用户代金券状态为'unused'（未使用）
4. **购买时间**: 记录购买时间作为获得时间

### 代金券属性继承
1. **面额继承**: 用户代金券继承原代金券的面额
2. **使用条件继承**: 继承最低消费金额等使用条件
3. **适用范围继承**: 继承适用的服务类型和范围
4. **有效期继承**: 继承有效期设置

## 代金券使用规则

### 使用条件检查
1. **状态检查**: 代金券状态必须为'unused'（未使用）
2. **有效期检查**: 如果设置了有效期，必须在有效期内
3. **最低消费检查**: 订单金额必须满足代金券的最低消费要求
4. **适用性检查**: 检查代金券是否适用于当前服务

### 使用扣减规则
1. **一次性使用**: 代金券只能使用一次
2. **全额抵扣**: 在满足使用条件的情况下，按面额全额抵扣
3. **状态更新**: 使用后代金券状态变为'used'（已使用）
4. **使用记录**: 记录使用时间和关联订单

### 使用限制
1. **单次使用**: 每张代金券只能在一个订单中使用
2. **不找零**: 代金券金额大于订单金额时不找零
3. **不叠加**: 原则上一个订单只能使用一张代金券
4. **不转让**: 代金券不能转让给其他用户

## 代金券过期处理

### 自动过期检查
1. **定期检查**: 系统定期检查所有代金券的有效期
2. **过期更新**: 超过有效期的代金券自动更新状态为'expired'
3. **过期通知**: 可以在代金券即将过期时通知用户

### 过期规则
1. **时间过期**: 超过validTo时间的代金券自动过期
2. **手动过期**: 管理员可以手动设置代金券过期
3. **批量过期**: 支持批量处理过期代金券

## 退款规则

### 退款条件
1. **状态限制**: 只有"已付款"状态的订单可以申请退款
2. **使用检查**: 已使用的代金券原则上不允许退款
3. **管理员权限**: 管理员可以对任何状态的订单进行强制退款

### 退款流程
1. **申请退款**: 用户或管理员申请退款
2. **状态变更**: 订单状态变为"已退款"
3. **代金券回收**: 回收已发放的代金券（状态变为'expired'）
4. **退款处理**: 执行微信退款操作

### 退款金额计算
1. **全额退款**: 退还用户实际支付的全部金额
2. **部分退款**: 根据代金券使用情况计算退款金额
3. **使用扣减**: 已使用的代金券可能扣减相应费用

## 管理员操作规则

### 管理员权限
1. **最大权限**: 管理员对代金券订单拥有最大操作权限
2. **任何状态**: 可以对任何状态的订单进行操作
3. **强制操作**: 可以强制退款、删除等操作

### 代金券管理
1. **手动发放**: 管理员可以手动为用户发放代金券
2. **禁用代金券**: 管理员可以禁用用户的代金券
3. **延期处理**: 管理员可以延长代金券有效期
4. **操作记录**: 所有管理员操作都会记录操作日志

### 数据一致性
1. **事务处理**: 关键操作使用数据库事务确保一致性
2. **状态同步**: 确保订单状态与代金券状态的一致性
3. **库存管理**: 如果代金券有数量限制，确保库存一致性

## 特殊场景处理

### 支付异常处理
1. **微信支付成功但本地更新失败**:
   - 提供手动同步接口
   - 防止重复发放代金券
   - 记录异常日志
2. **重复支付防护**:
   - 检查订单支付状态
   - 防止重复支付操作
   - 记录支付历史

### 并发处理
1. **订单创建**: 使用数据库锁防止重复创建
2. **状态更新**: 确保状态更新的原子性
3. **代金券发放**: 防止重复发放代金券
4. **库存扣减**: 处理代金券库存的并发扣减

### 数据恢复
1. **订单状态恢复**: 提供状态回滚机制
2. **支付状态同步**: 定期同步微信支付状态
3. **代金券修复**: 提供代金券数据修复工具

## 代金券在订单中的使用

### 订单优惠计算
1. **优惠顺序**: 先计算权益卡折扣，再计算代金券抵扣
2. **金额验证**: 验证代金券抵扣金额不超过订单金额
3. **最低消费**: 验证订单金额满足代金券最低消费要求
4. **适用性**: 验证代金券适用于订单中的服务

### 使用记录创建
1. **优惠信息记录**: 在订单优惠信息表中记录代金券使用
2. **代金券状态更新**: 更新代金券状态为已使用
3. **使用时间记录**: 记录代金券使用时间
4. **关联订单**: 记录代金券使用的订单ID

### 退款时的代金券处理
1. **全额退款**: 退还代金券到用户账户
2. **部分退款**: 根据退款比例决定是否退还代金券
3. **状态恢复**: 退还的代金券状态恢复为未使用
4. **有效期检查**: 退还时检查代金券是否仍在有效期内

## 性能优化规则

### 查询优化
1. **索引优化**: 在常用查询字段上建立索引
2. **关联查询**: 优化代金券和客户信息的关联查询
3. **分页查询**: 大数据量查询必须使用分页
4. **状态筛选**: 优化按状态筛选的查询性能

### 缓存策略
1. **代金券信息**: 缓存启用的代金券信息
2. **用户代金券**: 缓存用户的可用代金券列表
3. **适用性计算**: 缓存代金券适用性计算结果
4. **使用条件**: 缓存代金券使用条件检查结果

### 数据库事务
1. **订单创建**: 订单创建和代金券发放使用事务
2. **退款操作**: 退款和代金券回收使用事务
3. **使用操作**: 代金券使用和订单创建使用事务
4. **状态更新**: 批量状态更新使用事务

## 监控和告警

### 业务监控
1. **订单量监控**: 监控代金券订单创建和支付量
2. **使用率监控**: 监控代金券的使用率
3. **过期率监控**: 监控代金券的过期率
4. **退款率**: 监控代金券订单退款率

### 系统监控
1. **接口性能**: 监控关键接口的响应时间
2. **错误率**: 监控接口错误率
3. **支付成功率**: 监控微信支付成功率
4. **库存监控**: 监控代金券库存状态

### 告警机制
1. **支付异常**: 支付失败率过高时告警
2. **代金券异常**: 代金券发放失败时告警
3. **库存不足**: 代金券库存不足时告警
4. **数据不一致**: 发现数据不一致时告警
