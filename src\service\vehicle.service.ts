import { Provide } from '@midwayjs/core';
import { Vehicle } from '../entity/vehicle.entity';
import { BaseService } from '../common/BaseService';
import { Employee } from '../entity/employee.entity';
import { EmployeePosition } from '../common/Constant';

@Provide()
export class VehicleService extends BaseService<Vehicle> {
  constructor() {
    super('车辆');
  }

  getModel() {
    return Vehicle;
  }

  /**
   * 根据业务规则选择主要员工
   * 1. 无关联员工时为null
   * 2. 有一个关联员工时直接为这个员工信息
   * 3. 有多个关联员工时，先按创建时间升序排序，取第一个美容师信息，无美容师时取第一个洗护师，也没有时无条件取第一个
   */
  private selectPrimaryEmployee(employees: Employee[]): Employee | null {
    if (!employees || employees.length === 0) {
      return null;
    }

    if (employees.length === 1) {
      return employees[0];
    }

    // 按创建时间升序排序
    const sortedEmployees = employees.sort((a, b) => {
      const timeA = new Date(a.createdAt).getTime();
      const timeB = new Date(b.createdAt).getTime();
      return timeA - timeB;
    });

    // 优先选择美容师
    const beautician = sortedEmployees.find(
      emp => emp.position === EmployeePosition.美容师
    );
    if (beautician) {
      return beautician;
    }

    // 其次选择洗护师
    const groomer = sortedEmployees.find(
      emp => emp.position === EmployeePosition.洗护师
    );
    if (groomer) {
      return groomer;
    }

    // 都没有时取第一个
    return sortedEmployees[0];
  }

  async findByPlateNumber(plateNumber: string) {
    return await this.findOne({ where: { plateNumber } });
  }

  async updateLocation(id: number, latitude: number, longitude: number) {
    return await this.update({ id }, { latitude, longitude });
  }

  async updateStatus(id: number, status: string) {
    return await this.update({ id }, { status });
  }

  async getEmployee(vehicleId: number) {
    const vehicle = await Vehicle.findByPk(vehicleId, {
      include: [Employee],
    });
    const employees = vehicle?.employees || [];

    // 返回包含兼容性字段的结果
    return {
      employees: employees,
      employee: this.selectPrimaryEmployee(employees), // 兼容性字段
    };
  }

  /**
   * 员工端更新车辆信息
   * @param vehicleId 车辆ID
   * @param employeeId 员工ID
   * @param updateData 更新数据
   */
  async updateVehicleInfo(
    vehicleId: number,
    employeeId: number,
    updateData: any
  ) {
    const now = new Date();
    const updateInfo = {
      ...updateData,
      lastSubmittedAt: now,
      lastSubmittedBy: employeeId,
    };

    return await this.update({ id: vehicleId }, updateInfo);
  }

  /**
   * 获取车辆详细信息（包含提交记录）
   * @param vehicleId 车辆ID
   */
  async getVehicleDetail(vehicleId: number) {
    const vehicle = await Vehicle.findByPk(vehicleId, {
      include: [
        {
          model: Employee,
          as: 'employees',
        },
      ],
    });

    if (!vehicle) {
      return null;
    }

    // 获取最后提交人信息
    let lastSubmittedEmployee = null;
    if (vehicle.lastSubmittedBy) {
      lastSubmittedEmployee = await Employee.findByPk(vehicle.lastSubmittedBy, {
        attributes: ['id', 'name', 'phone'],
      });
    }

    const vehicleData = vehicle.toJSON() as any;

    // 添加兼容性 employee 字段
    if (vehicleData.employees) {
      vehicleData.employee = this.selectPrimaryEmployee(vehicleData.employees);
    } else {
      vehicleData.employee = null;
    }

    return {
      ...vehicleData,
      lastSubmittedEmployee,
    };
  }

  /**
   * 重写 findAll 方法，为车辆查询结果添加兼容性 employee 字段
   */
  async findAll(params: {
    query: any;
    attributes?: any;
    include?: any;
    offset?: number;
    limit?: number;
    order?: any;
    sort?: any;
    filter?: any;
  }): Promise<{ list: Vehicle[]; total?: number }> {
    const result = await super.findAll(params);

    // 为每个车辆添加兼容性 employee 字段
    const processedList = result.list.map(vehicle => {
      const vehicleData = vehicle.toJSON() as any;

      // 如果有 employees 数据，根据业务规则选择主要员工
      if (vehicleData.employees) {
        vehicleData.employee = this.selectPrimaryEmployee(
          vehicleData.employees
        );
      } else {
        vehicleData.employee = null;
      }

      return vehicleData;
    });

    return {
      list: processedList as Vehicle[],
      total: result.total,
    };
  }
}
