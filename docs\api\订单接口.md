# 订单相关API接口

## 订单状态枚举
```typescript
enum OrderStatus {
  待付款 = '待付款',
  待接单 = '待接单', 
  待服务 = '待服务',
  已出发 = '已出发',
  服务中 = '服务中',
  已完成 = '已完成',
  已评价 = '已评价',
  已取消 = '已取消',
  退款中 = '退款中',
  已退款 = '已退款'
}
```

## 1. 订单查询接口

### 1.1 查询所有订单列表
- **接口**: `GET /orders`
- **描述**: 管理端查询所有订单列表，支持分页和筛选
- **参数**:
  - `current` (number): 页码，默认1
  - `pageSize` (number): 每页数量，默认10
  - `phone` (string): 客户手机号筛选
  - `customerName` (string): 客户昵称模糊查找
  - `employeename` (string): 员工姓名模糊查找
  - `serviceType` (string): 服务大类型筛选（如：美容、洗护）
  - `startTime` (string): 开始时间筛选（ISO格式，如：2024-01-01T00:00:00.000Z）
  - `endTime` (string): 结束时间筛选（ISO格式，如：2024-12-31T23:59:59.999Z）
  - `status` (string): 订单状态筛选
  - `filter` (string): JSON格式筛选条件
  - `includeAdditionalServices` (string): 是否包含追加服务详情，传 'true' 启用

**新增筛选功能说明**:
- **时间范围筛选**: 使用 `startTime` 和 `endTime` 参数按订单创建时间筛选，支持单独使用或组合使用
- **用户名称模糊查找**: 使用 `customerName` 参数按客户昵称进行模糊匹配
- **服务人员模糊查找**: 使用 `employeename` 参数按员工姓名进行模糊匹配
- **服务大类型筛选**: 使用 `serviceType` 参数按服务类型筛选，常见值包括：
  - `美容`: 美容类服务
  - `洗护`: 洗护类服务
  - 其他服务类型根据 ServiceType 表中的 type 字段值确定

### 1.2 导出订单Excel
- **接口**: `GET /orders/export`
- **描述**: 导出订单列表为Excel文件，支持与查询接口相同的筛选条件
- **参数**:
  - 支持所有 `/orders` 接口的筛选参数（除了分页参数 `current` 和 `pageSize`）
  - `phone` (string): 客户手机号筛选
  - `customerName` (string): 客户昵称模糊查找
  - `employeename` (string): 员工姓名模糊查找
  - `serviceType` (string): 服务大类型筛选
  - `startTime` (string): 开始时间筛选
  - `endTime` (string): 结束时间筛选
  - `status` (string): 订单状态筛选
  - `filter` (string): JSON格式筛选条件
- **返回**: Excel文件下载
- **Excel内容**:
  - 标题：订单导出列表
  - 列：订单编号、客户姓名、客户手机、员工姓名、订单状态、下单时间、服务时间、服务地址、订单原价、实付金额、服务项目
  - 文件名格式：`orders_YYYYMMDD_HHmmss.xlsx`

### 1.2 查询订单详情
- **接口**: `GET /orders/{orderId}`
- **描述**: 查询指定订单的详细信息
- **参数**: `orderId` (number): 订单ID

### 1.3 查询员工订单列表
- **接口**: `GET /orders/{employeeId}`
- **描述**: 员工端查询可接单列表
- **参数**: 
  - `employeeId` (number): 员工ID
  - `current` (number): 页码
  - `pageSize` (number): 每页数量
  - `type` (string): 订单类型

### 1.4 按状态查询员工订单
- **接口**: `GET /orders/employee/{employeeId}`
- **描述**: 按状态查询员工名下的订单列表
- **参数**: 
  - `employeeId` (number): 员工ID
  - `status` (string): 状态列表，逗号分隔

### 1.5 查询客户订单详情
- **接口**: `GET /customers/{id}/order/{orderId}`
- **描述**: 获取指定客户的订单详情
- **参数**: 
  - `id` (number): 客户ID
  - `orderId` (number): 订单ID

### 1.6 查询客户订单列表
- **接口**: `GET /customers/{customerId}/orders`
- **描述**: 查询指定客户的订单列表
- **参数**: 
  - `customerId` (number): 客户ID
  - `current` (number): 页码
  - `pageSize` (number): 每页数量
  - `status` (string): 状态筛选

## 2. 订单操作接口

### 2.1 创建订单
- **接口**: `POST /customers/{customerId}/order`
- **描述**: 为指定客户创建订单
- **参数**:
  - `customerId` (number): 客户ID
- **请求体**:
  ```typescript
  {
    customerId: number;           // 客户ID
    employeeId?: number;          // 员工ID（可选）
    orderTime: Date;              // 下单时间
    serviceTime: Date | null;     // 预约服务时间
    addressId?: number;           // 地址ID（可选）
    address: string;              // 服务地址（必填）
    longitude: number;            // 服务地址经度（必填）
    latitude: number;             // 服务地址纬度（必填）
    addressDetail: string;        // 服务地址详情（必填）
    addressRemark?: string;       // 服务地址备注
    originalPrice: number;        // 订单原价
    totalFee: number;             // 订单总费用
    cardDeduction: number;        // 权益卡抵扣金额
    couponDeduction: number;      // 代金券抵扣金额
    orderDetails: CreateOrderDetail[];  // 订单明细列表
    discountInfos?: Array<{       // 优惠信息列表
      discountType: DiscountType;
      discountId: number;
      discountAmount: number;
    }>;
  }

  // 订单明细结构
  CreateOrderDetail {
    serviceId: number;            // 服务ID
    serviceName: string;          // 服务名称
    servicePrice: number;         // 服务价格
    petId: number;                // 宠物ID
    petName: string;              // 宠物名称
    petType: string;              // 宠物类型
    petBreed: string;             // 宠物品种
    orderTime: Date;              // 下单时间
    userRemark?: string;          // 用户备注
    remarkPhotos?: string[];      // 说明图片URL数组（最多3张）
    addServiceIds: number[][];    // 增项服务ID列表
  }
  ```

### 2.2 接单
- **接口**: `POST /orders/{orderId}/accept`
- **描述**: 员工接单操作
- **参数**: 
  - `orderId` (number): 订单ID
  - `employeeId` (number): 员工ID

### 2.3 出发
- **接口**: `POST /orders/{orderId}/depart`
- **描述**: 员工出发操作
- **参数**: 
  - `orderId` (number): 订单ID
  - `employeeId` (number): 员工ID

### 2.4 完成订单
- **接口**: `POST /orders/{orderId}/complete`
- **描述**: 员工完成订单操作
- **参数**: 
  - `orderId` (number): 订单ID
  - `employeeId` (number): 员工ID
  - `afterPhotos` (string[]): 服务后照片

### 2.5 取消订单
- **接口**: `POST /orders/{orderId}/cancel`
- **描述**: 取消订单操作
- **参数**: `orderId` (number): 订单ID

### 2.6 删除订单
- **接口**: `DELETE /orders/{orderId}`
- **描述**: 删除订单（管理员权限）
- **参数**: `orderId` (number): 订单ID

### 2.7 修改订单地址
- **接口**: `PUT /orders/{orderId}/address`
- **描述**: 修改订单地址信息
- **参数**: 
  - `orderId` (number): 订单ID
- **请求体**: 包含新的地址信息

## 3. 服务计时接口

### 3.1 开始整体订单服务
- **接口**: `POST /employee/service-duration/start-order-service`
- **描述**: 开始整体订单服务，自动开始所有主服务计时
- **参数**: 
  - `orderId` (number): 订单ID
  - `beforePhotos` (string[]): 服务前照片

### 3.2 开始单个服务计时
- **接口**: `POST /employee/service-duration/start`
- **描述**: 开始单个服务项目计时
- **参数**: 包含服务类型、服务ID等信息

### 3.3 结束单个服务计时
- **接口**: `POST /employee/service-duration/end`
- **描述**: 结束单个服务项目计时
- **参数**: 
  - `recordId` (number): 计时记录ID
  - `remark` (string): 备注

### 3.4 查询订单服务记录
- **接口**: `GET /employee/service-duration/records/{orderId}`
- **描述**: 查询指定订单的所有服务时长记录
- **参数**: `orderId` (number): 订单ID

### 3.5 查询订单服务状态
- **接口**: `GET /employee/service-duration/order-service-status/{orderId}`
- **描述**: 查询订单服务状态和所有服务项目
- **参数**: `orderId` (number): 订单ID

### 3.6 查询员工当前服务
- **接口**: `GET /employee/service-duration/current-services`
- **描述**: 查询员工当前正在进行的服务
- **参数**: 无（从token获取员工ID）

## 4. 追加服务接口

### 4.1 申请追加服务
- **接口**: `POST /order-details/{orderDetailId}/additional-services`
- **描述**: 用户申请追加服务
- **参数**: 
  - `orderDetailId` (number): 订单详情ID
- **请求体**: 包含追加服务信息

### 4.2 确认追加服务
- **接口**: `POST /order-details/{orderDetailId}/additional-services/{id}/confirm`
- **描述**: 员工确认追加服务申请
- **参数**: 
  - `orderDetailId` (number): 订单详情ID
  - `id` (number): 追加服务ID
  - `result` (boolean): 确认结果

### 4.3 拒绝追加服务
- **接口**: `POST /order-details/{orderDetailId}/additional-services/{id}/reject`
- **描述**: 员工拒绝追加服务申请
- **参数**: 
  - `orderDetailId` (number): 订单详情ID
  - `id` (number): 追加服务ID
  - `reason` (string): 拒绝原因

### 4.4 查询追加服务列表
- **接口**: `GET /order-details/{orderDetailId}/additional-services`
- **描述**: 查询指定订单详情的所有追加服务（包括主订单增项服务和后续追加的服务）
- **参数**: `orderDetailId` (number): 订单详情ID
- **修复说明**: 已修复实体关联问题，现在可以正确获取增项服务信息
- **返回数据结构**:
```json
{
  "originalAdditionalServices": [
    {
      "id": 1,
      "name": "局部去油",
      "logo": "http://example.com/logo.jpg",
      "price": 15.00,
      "type": "cleaning",
      "description": "局部去油服务",
      "duration": 30,
      "needDurationTracking": true,
      "durationStatus": "completed",
      "recordId": 123
    }
  ],
  "additionalServiceOrders": [
    {
      "id": 41,
      "sn": "ADD17252410888292746",
      "status": "paid",
      "totalFee": 6.00,
      "originalPrice": 30.00,
      "cardDeduction": 24.00,
      "couponDeduction": 0.00,
      "confirmTime": "2025-07-13T13:58:20.000Z",
      "createdAt": "2025-07-13T13:58:08.000Z",
      "details": [
        {
          "id": "43_1",
          "originalDetailId": 43,
          "serviceId": 3,
          "serviceName": "刷牙",
          "servicePrice": 15.00,
          "sequenceNumber": 1,
          "totalQuantity": 2,
          "additionalServiceId": 1,
          "additionalServiceName": "局部去油",
          "logo": "http://example.com/logo.jpg",
          "price": 15.00,
          "type": "cleaning",
          "description": "局部去油服务",
          "duration": 30,
          "needDurationTracking": true,
          "durationStatus": "not_started",
          "durationRecord": null
        },
        {
          "id": "43_2",
          "originalDetailId": 43,
          "serviceId": 3,
          "serviceName": "刷牙",
          "servicePrice": 15.00,
          "sequenceNumber": 2,
          "totalQuantity": 2,
          "additionalServiceId": 1,
          "additionalServiceName": "局部去油",
          "logo": "http://example.com/logo.jpg",
          "price": 15.00,
          "type": "cleaning",
          "description": "局部去油服务",
          "duration": 30,
          "needDurationTracking": true,
          "durationStatus": "not_started",
          "durationRecord": null
        }
      ]
    }
  ],
  "summary": {
    "originalCount": 1,
    "additionalOrdersCount": 1,
    "totalCount": 2
  }
}
```

**字段说明**:
- `originalAdditionalServices`: 主订单时选择的增项服务列表
  - `needDurationTracking`: 是否需要计时
  - `durationStatus`: 计时状态 (`not_started` | `in_progress` | `completed`)
  - `recordId`: 计时记录ID
- `additionalServiceOrders`: 后续追加的服务订单列表
  - `details`: 按数量展开的服务明细（每个数量对应一个独立项）
  - `sequenceNumber`: 序号（用于区分同一服务的多次记录）
  - `totalQuantity`: 该服务的总数量
  - `additionalServiceId`: 增项服务ID（现在通过正确的实体关联获取）
  - `additionalServiceName`: 增项服务名称（现在通过正确的实体关联获取）
  - `durationStatus`: 计时状态 (`not_started` | `in_progress` | `completed` | `not_required`)
  - `durationRecord`: 计时记录详情（如果有）

### 4.5 查询追加服务详情
- **接口**: `GET /order-details/{orderDetailId}/additional-services/{id}`
- **描述**: 查询指定追加服务的详细信息
- **参数**: 
  - `orderDetailId` (number): 订单详情ID
  - `id` (number): 追加服务ID

## 5. 退款相关接口

### 5.1 申请退款
- **接口**: `POST /customers/{customerId}/applyRefund/{sn}`
- **描述**: 用户申请主订单退款
- **参数**: 
  - `customerId` (number): 客户ID
  - `sn` (string): 订单号

### 5.2 审核退款
- **接口**: `POST /orders/{sn}/auditRefund`
- **描述**: 管理端审核主订单退款申请
- **参数**: 
  - `sn` (string): 订单号
- **请求体**: 包含审核结果和退款金额

### 5.3 追加服务退款
- **接口**: `POST /order-details/{orderDetailId}/additional-services/{id}/refund`
- **描述**: 追加服务退款
- **参数**:
  - `orderDetailId` (number): 订单详情ID
  - `id` (number): 追加服务ID

### 5.4 获取订单退款信息（管理端）
- **接口**: `GET /admin/orders/{orderId}/refund-info`
- **描述**: 获取订单的详细退款信息，包括主订单和所有追加服务
- **参数**:
  - `orderId` (number): 订单ID
- **功能**:
  - 显示主订单和追加服务的退款信息
  - 计算可退款金额
  - 显示卡券使用情况

### 5.5 统一退款审核（管理端）⭐ 新功能
- **接口**: `POST /admin/orders/refund-audit`
- **描述**: 统一处理主订单和追加服务的退款审核
- **功能**:
  - 支持同时退款主订单和追加服务
  - 支持设置不同的退款金额
  - 支持指定是否退还卡券
  - 支持批量退款审核
- **请求体**:
  ```typescript
  {
    operatorId: number;                    // 操作员ID
    result: boolean;                       // 审核结果
    reason?: string;                       // 审核原因
    refundItems: Array<{                   // 退款项目
      type: 'main_order' | 'additional_service';
      id: string | number;                 // 主订单sn或追加服务id
      refundAmount?: number;               // 退款金额（可选）
      shouldRefundCoupons?: boolean;       // 是否退还卡券（可选）
    }>;
  }
  ```

## 6. 支付相关接口

### 6.1 查询支付状态
- **接口**: `GET /orders/payment-status/{sn}`
- **描述**: 查询订单支付状态
- **参数**: `sn` (string): 订单号

### 6.2 同步支付状态
- **接口**: `POST /orders/sync-payment-status/{sn}`
- **描述**: 同步微信支付状态
- **参数**: `sn` (string): 订单号

## 7. 订单日志接口

### 7.1 获取订单日志
- **接口**: `GET /orders/{orderId}/logs`
- **描述**: 获取订单操作日志
- **参数**: `orderId` (number): 订单ID
