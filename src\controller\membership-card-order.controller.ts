import {
  Controller,
  Get,
  Post,
  Put,
  Inject,
  Param,
  Body,
  Query,
} from '@midwayjs/core';
import { Op } from 'sequelize';
import { MembershipCardOrderService } from '../service/membership-card-order.service';
import { MembershipCardOrderStatus } from '../entity/membership-card-order.entity';
import { CustomError } from '../error/custom.error';
import { Customer } from '../entity/customer.entity';

@Controller('/membership-card-orders')
export class MembershipCardOrderController {
  @Inject()
  service: MembershipCardOrderService;

  @Get('/all', { summary: '查询所有权益卡订单列表' })
  async findAll(
    @Query('status') status: string,
    @Query('current') current: number,
    @Query('pageSize') pageSize: number,
    @Query('sn') sn: string,
    @Query('customerInfo') customerInfo: string
  ) {
    const query: any = {};
    if (status) {
      query.status = status.split(',');
    }

    // 处理订单编号模糊查询
    if (sn) {
      query.sn = { [Op.like]: `%${sn}%` };
    }

    // 构建include选项，处理用户信息模糊查询
    const includeOptions: any[] = [
      {
        model: Customer,
        as: 'customer',
        where: customerInfo
          ? {
              [Op.or]: [
                { nickname: { [Op.like]: `%${customerInfo}%` } },
                { phone: { [Op.like]: `%${customerInfo}%` } },
              ],
            }
          : undefined,
        required: !!customerInfo, // 如果有customerInfo查询条件，则必须匹配
      },
      'cardType',
    ];

    return await this.service.findAll({
      query,
      offset: (current - 1) * pageSize,
      limit: pageSize,
      order: [['updatedAt', 'DESC']],
      include: includeOptions,
    });
  }

  @Get('/', { summary: '查询权益卡订单列表' })
  async index(
    @Query('customerId') customerId: number,
    @Query('status') status: string,
    @Query('current') current: number,
    @Query('pageSize') pageSize: number
  ) {
    if (!customerId) {
      throw new CustomError('用户ID不能为空');
    }

    let statusArray: MembershipCardOrderStatus[] = [];
    if (status) {
      statusArray = status.split(',') as MembershipCardOrderStatus[];
    }

    return await this.service.findCustomerOrders(
      customerId,
      statusArray,
      current,
      pageSize
    );
  }

  @Get('/:id', { summary: '按ID查询权益卡订单' })
  async show(@Param('id') id: number) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定权益卡订单');
    }
    return res;
  }

  @Post('/', { summary: '创建权益卡订单' })
  async create(
    @Body()
    body: {
      customerId: number;
      cardTypeId: number;
      remark?: string;
    }
  ) {
    const { customerId, cardTypeId, remark } = body;
    if (!customerId) {
      throw new CustomError('用户ID不能为空');
    }
    if (!cardTypeId) {
      throw new CustomError('权益卡类型ID不能为空');
    }

    return await this.service.createOrder(customerId, cardTypeId, remark);
  }

  @Post('/:sn/pay', { summary: '支付权益卡订单' })
  async pay(@Param('sn') sn: string, @Body('customerId') customerId: number) {
    if (!customerId) {
      throw new CustomError('用户ID不能为空');
    }

    return await this.service.payOrder(customerId, sn);
  }

  @Put('/:sn/cancel', { summary: '取消权益卡订单' })
  async cancel(
    @Param('sn') sn: string,
    @Body('customerId') customerId: number
  ) {
    if (!customerId) {
      throw new CustomError('用户ID不能为空');
    }

    return await this.service.cancelOrder(customerId, sn);
  }
}
