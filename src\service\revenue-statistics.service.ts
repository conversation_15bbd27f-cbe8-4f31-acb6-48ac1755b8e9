import { Provide, Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { Op, fn, col, literal, QueryTypes } from 'sequelize';
import { Order, AdditionalServiceOrder } from '../entity';
import { OrderStatus } from '../common/Constant';
import { AdditionalServiceOrderStatus } from '../entity/additional-service-order.entity';

@Provide()
export class RevenueStatisticsService {
  @Inject()
  ctx: Context;

  /**
   * 获取收入概览统计
   */
  async getRevenueOverview(startDate?: string, endDate?: string) {
    // 构建时间条件
    let timeCondition = '';
    const params: any[] = [];

    if (startDate && endDate) {
      timeCondition = 'AND o.createdAt BETWEEN ? AND ?';
      params.push(new Date(startDate), new Date(endDate + ' 23:59:59'));
    }

    // 数据一致性验证查询
    const validationQuery = `
      SELECT
        COUNT(*) as totalCount,
        COUNT(CASE WHEN ABS(o.totalFee - (o.originalPrice - o.cardDeduction - o.couponDeduction)) > 0.01 THEN 1 END) as inconsistentCount,
        SUM(o.originalPrice) as sumOriginalPrice,
        SUM(o.cardDeduction + o.couponDeduction) as sumDiscounts,
        SUM(o.totalFee) as sumTotalFee,
        SUM(o.originalPrice - o.cardDeduction - o.couponDeduction) as calculatedTotalFee
      FROM orders o
      WHERE o.status IN ('已完成', '已评价', '退款中', '已退款')
      ${timeCondition}
    `;

    const validationResult = (await Order.sequelize.query(validationQuery, {
      replacements: params,
      type: QueryTypes.SELECT,
    })) as any[];

    console.log('=== 主订单数据一致性验证 ===');
    console.log('总订单数:', validationResult[0].totalCount);
    console.log('数据不一致订单数:', validationResult[0].inconsistentCount);
    console.log('原价总和:', validationResult[0].sumOriginalPrice);
    console.log('优惠总和:', validationResult[0].sumDiscounts);
    console.log('实付总和:', validationResult[0].sumTotalFee);
    console.log('计算实付总和:', validationResult[0].calculatedTotalFee);
    console.log(
      '差异:',
      parseFloat(validationResult[0].sumTotalFee) -
        parseFloat(validationResult[0].calculatedTotalFee)
    );

    // 追加服务数据一致性验证查询
    const additionalValidationQuery = `
      SELECT
        COUNT(*) as totalCount,
        COUNT(CASE WHEN ABS(aso.totalFee - (aso.originalPrice - aso.cardDeduction - aso.couponDeduction)) > 0.01 THEN 1 END) as inconsistentCount,
        SUM(aso.originalPrice) as sumOriginalPrice,
        SUM(aso.cardDeduction + aso.couponDeduction) as sumDiscounts,
        SUM(aso.totalFee) as sumTotalFee,
        SUM(aso.originalPrice - aso.cardDeduction - aso.couponDeduction) as calculatedTotalFee
      FROM additional_service_orders aso
      WHERE aso.status IN ('completed', 'refunding', 'refunded')
      ${timeCondition.replace('o.', 'aso.')}
    `;

    const additionalValidationResult = (await Order.sequelize.query(
      additionalValidationQuery,
      {
        replacements: params,
        type: QueryTypes.SELECT,
      }
    )) as any[];

    console.log('=== 追加服务数据一致性验证 ===');
    console.log('总订单数:', additionalValidationResult[0].totalCount);
    console.log(
      '数据不一致订单数:',
      additionalValidationResult[0].inconsistentCount
    );
    console.log('原价总和:', additionalValidationResult[0].sumOriginalPrice);
    console.log('优惠总和:', additionalValidationResult[0].sumDiscounts);
    console.log('实付总和:', additionalValidationResult[0].sumTotalFee);
    console.log(
      '计算实付总和:',
      additionalValidationResult[0].calculatedTotalFee
    );
    console.log(
      '差异:',
      parseFloat(additionalValidationResult[0].sumTotalFee) -
        parseFloat(additionalValidationResult[0].calculatedTotalFee)
    );

    // 主订单收入统计 - 包含已完成、已评价、退款中、已退款
    const mainOrderQuery = `
      SELECT
        COUNT(o.id) as orderCount,
        SUM(o.originalPrice) as totalOriginalPrice,
        SUM(o.totalFee) as totalPaidAmount,
        SUM(o.cardDeduction) as totalCardDeduction,
        SUM(o.couponDeduction) as totalCouponDeduction,
        SUM(CASE WHEN o.status IN ('已完成', '已评价') THEN o.totalFee ELSE 0 END) as effectiveRevenue,
        SUM(CASE WHEN o.status IN ('退款中', '已退款') THEN o.totalFee ELSE 0 END) as refundedAmount,
        COUNT(CASE WHEN o.status IN ('已完成', '已评价') THEN 1 ELSE NULL END) as effectiveOrderCount,
        COUNT(CASE WHEN o.status IN ('退款中', '已退款') THEN 1 ELSE NULL END) as refundedOrderCount
      FROM orders o
      WHERE o.status IN ('已完成', '已评价', '退款中', '已退款')
      ${timeCondition}
    `;

    const mainOrderStats = (await Order.sequelize.query(mainOrderQuery, {
      replacements: params,
      type: QueryTypes.SELECT,
    })) as any[];

    // 追加服务收入统计 - 注意：追加服务只有'已完成'状态对应主订单的'已完成'和'已评价'
    const additionalServiceQuery = `
      SELECT
        COUNT(aso.id) as orderCount,
        SUM(aso.originalPrice) as totalOriginalPrice,
        SUM(aso.totalFee) as totalPaidAmount,
        SUM(aso.cardDeduction) as totalCardDeduction,
        SUM(aso.couponDeduction) as totalCouponDeduction,
        SUM(CASE WHEN aso.status = 'completed' THEN aso.totalFee ELSE 0 END) as effectiveRevenue,
        SUM(CASE WHEN aso.status IN ('refunding', 'refunded') THEN aso.totalFee ELSE 0 END) as refundedAmount,
        COUNT(CASE WHEN aso.status = 'completed' THEN 1 ELSE NULL END) as effectiveOrderCount,
        COUNT(CASE WHEN aso.status IN ('refunding', 'refunded') THEN 1 ELSE NULL END) as refundedOrderCount
      FROM additional_service_orders aso
      WHERE aso.status IN ('completed', 'refunding', 'refunded')
      ${timeCondition.replace('o.', 'aso.')}
    `;

    const additionalServiceStats = (await Order.sequelize.query(
      additionalServiceQuery,
      {
        replacements: params,
        type: QueryTypes.SELECT,
      }
    )) as any[];

    // 获取统计结果
    const mainStats = mainOrderStats[0] || {};
    const additionalStats = additionalServiceStats[0] || {};

    // 按照6个核心概念计算数据

    // 1. 总原价：所有订单的原价（包含退款订单）
    const mainOriginalPrice = parseFloat(mainStats.totalOriginalPrice || '0');
    const additionalOriginalPrice = parseFloat(
      additionalStats.totalOriginalPrice || '0'
    );
    const totalOriginalPrice = mainOriginalPrice + additionalOriginalPrice;

    // 2. 优惠金额：所有订单的优惠（包含退款订单）
    const mainCardDeduction = parseFloat(mainStats.totalCardDeduction || '0');
    const additionalCardDeduction = parseFloat(
      additionalStats.totalCardDeduction || '0'
    );
    const mainCouponDeduction = parseFloat(
      mainStats.totalCouponDeduction || '0'
    );
    const additionalCouponDeduction = parseFloat(
      additionalStats.totalCouponDeduction || '0'
    );
    const totalDiscount =
      mainCardDeduction +
      additionalCardDeduction +
      mainCouponDeduction +
      additionalCouponDeduction;

    // 3. 优惠率：优惠金额/总原价
    const discountRate =
      totalOriginalPrice > 0
        ? ((totalDiscount / totalOriginalPrice) * 100).toFixed(2)
        : '0.00';

    // 4. 实收金额：所有订单的实付总和（应该等于总原价-优惠金额）
    const mainPaidAmount = parseFloat(mainStats.totalPaidAmount || '0');
    const additionalPaidAmount = parseFloat(
      additionalStats.totalPaidAmount || '0'
    );
    const totalPaidAmount = mainPaidAmount + additionalPaidAmount;

    // 5. 退款金额：退款订单的实付金额
    const mainRefundedAmount = parseFloat(mainStats.refundedAmount || '0');
    const additionalRefundedAmount = parseFloat(
      additionalStats.refundedAmount || '0'
    );
    const totalRefundedAmount = mainRefundedAmount + additionalRefundedAmount;

    // 6. 净收入：实收金额 - 退款金额
    const netRevenue = totalPaidAmount - totalRefundedAmount;

    // 计算订单数量
    const mainOrderCount = parseInt(mainStats.orderCount || '0');
    const additionalOrderCount = parseInt(additionalStats.orderCount || '0');

    // 总体数据一致性验证
    const calculatedPaidAmount = totalOriginalPrice - totalDiscount;
    const paidAmountDifference = Math.abs(
      totalPaidAmount - calculatedPaidAmount
    );

    console.log('=== 总体数据一致性验证 ===');
    console.log('总原价:', totalOriginalPrice);
    console.log('总优惠:', totalDiscount);
    console.log('计算实收金额 (总原价-优惠):', calculatedPaidAmount);
    console.log('统计实收金额:', totalPaidAmount);
    console.log('差异:', paidAmountDifference);

    if (paidAmountDifference > 0.01) {
      console.warn(
        '⚠️  数据不一致：总原价-优惠金额 ≠ 实收金额，差异:',
        paidAmountDifference
      );
      console.warn('💡 问题诊断：');
      console.warn(
        `   - 主订单不一致数量: ${validationResult[0].inconsistentCount}`
      );
      console.warn(
        `   - 追加服务不一致数量: ${additionalValidationResult[0].inconsistentCount}`
      );
      console.warn('💡 建议解决方案：');
      console.warn(
        '   1. 运行订单金额异常检测: POST /admin/order-fix/check-amount-inconsistency'
      );
      console.warn('   2. 查看具体不一致订单详情');
      console.warn(
        '   3. 使用修复脚本: POST /admin/order-fix/fix-amount-inconsistency'
      );
    } else {
      console.log('✅ 数据一致性验证通过');
    }

    return {
      // 按照6个核心概念返回数据
      totalOriginalPrice, // 1. 总原价
      totalDiscount, // 2. 优惠金额
      discountRate, // 3. 优惠率
      totalPaidAmount, // 4. 实收金额
      totalRefundedAmount, // 5. 退款金额
      netRevenue, // 6. 净收入

      // 主订单数据
      mainOrder: {
        orderCount: mainOrderCount,
        paidAmount: mainPaidAmount,
        refundedAmount: mainRefundedAmount,
        totalOriginalPrice: mainOriginalPrice,
        totalDiscount: mainCardDeduction + mainCouponDeduction,
        avgOrderValue:
          mainOrderCount > 0
            ? (mainPaidAmount / mainOrderCount).toFixed(2)
            : '0.00',
      },

      // 追加服务数据
      additionalService: {
        orderCount: additionalOrderCount,
        paidAmount: additionalPaidAmount,
        refundedAmount: additionalRefundedAmount,
        totalOriginalPrice: additionalOriginalPrice,
        totalDiscount: additionalCardDeduction + additionalCouponDeduction,
        avgOrderValue:
          additionalOrderCount > 0
            ? (additionalPaidAmount / additionalOrderCount).toFixed(2)
            : '0.00',
      },

      // 总订单数据
      totalOrderCount: mainOrderCount + additionalOrderCount,
      avgOrderValue:
        mainOrderCount + additionalOrderCount > 0
          ? (totalPaidAmount / (mainOrderCount + additionalOrderCount)).toFixed(
              2
            )
          : '0.00',
    };
  }

  /**
   * 获取收入趋势统计
   */
  async getRevenueTrend(
    startDate?: string,
    endDate?: string,
    periodType: 'day' | 'week' | 'month' = 'day'
  ) {
    let dateFormat: string;

    switch (periodType) {
      case 'week':
        dateFormat = '%Y-%u';
        break;
      case 'month':
        dateFormat = '%Y-%m';
        break;
      default:
        dateFormat = '%Y-%m-%d';
    }

    const whereCondition: any = {
      status: {
        [Op.in]: [OrderStatus.已完成, OrderStatus.已评价],
      },
    };

    // 添加时间条件（如果提供了时间参数）
    if (startDate || endDate) {
      whereCondition.createdAt = {};
      if (startDate) {
        whereCondition.createdAt[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        whereCondition.createdAt[Op.lte] = new Date(endDate + ' 23:59:59');
      }
    }

    // 主订单趋势
    const mainOrderTrend = await Order.findAll({
      where: whereCondition,
      attributes: [
        [literal(`DATE_FORMAT(createdAt, '${dateFormat}')`), 'period'],
        [fn('COUNT', col('id')), 'orderCount'],
        [fn('SUM', col('totalFee')), 'totalRevenue'],
        [fn('SUM', col('originalPrice')), 'totalOriginalPrice'],
        [fn('SUM', col('cardDeduction')), 'totalCardDeduction'],
        [fn('SUM', col('couponDeduction')), 'totalCouponDeduction'],
      ],
      group: ['period'],
      order: [['period', 'ASC']],
      raw: true,
    });

    // 追加服务趋势
    const additionalServiceWhereCondition: any = {
      status: {
        [Op.in]: [AdditionalServiceOrderStatus.COMPLETED],
      },
    };

    // 添加时间条件（如果提供了时间参数）
    if (startDate || endDate) {
      additionalServiceWhereCondition.createdAt = {};
      if (startDate) {
        additionalServiceWhereCondition.createdAt[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        additionalServiceWhereCondition.createdAt[Op.lte] = new Date(endDate + ' 23:59:59');
      }
    }

    const additionalServiceTrend = await AdditionalServiceOrder.findAll({
      where: additionalServiceWhereCondition,
      attributes: [
        [literal(`DATE_FORMAT(createdAt, '${dateFormat}')`), 'period'],
        [fn('COUNT', col('id')), 'orderCount'],
        [fn('SUM', col('totalFee')), 'totalRevenue'],
        [fn('SUM', col('originalPrice')), 'totalOriginalPrice'],
        [fn('SUM', col('cardDeduction')), 'totalCardDeduction'],
        [fn('SUM', col('couponDeduction')), 'totalCouponDeduction'],
      ],
      group: ['period'],
      order: [['period', 'ASC']],
      raw: true,
    });

    // 合并数据
    const trendMap = new Map();

    // 处理主订单数据
    mainOrderTrend.forEach((item: any) => {
      const period = item.period;
      trendMap.set(period, {
        period,
        mainOrder: {
          orderCount: parseInt(item.orderCount),
          totalRevenue: parseFloat(item.totalRevenue || '0'),
          totalOriginalPrice: parseFloat(item.totalOriginalPrice || '0'),
          totalDiscount:
            parseFloat(item.totalCardDeduction || '0') +
            parseFloat(item.totalCouponDeduction || '0'),
        },
        additionalService: {
          orderCount: 0,
          totalRevenue: 0,
          totalOriginalPrice: 0,
          totalDiscount: 0,
        },
      });
    });

    // 处理追加服务数据
    additionalServiceTrend.forEach((item: any) => {
      const period = item.period;
      const existing = trendMap.get(period) || {
        period,
        mainOrder: {
          orderCount: 0,
          totalRevenue: 0,
          totalOriginalPrice: 0,
          totalDiscount: 0,
        },
        additionalService: {
          orderCount: 0,
          totalRevenue: 0,
          totalOriginalPrice: 0,
          totalDiscount: 0,
        },
      };

      existing.additionalService = {
        orderCount: parseInt(item.orderCount),
        totalRevenue: parseFloat(item.totalRevenue || '0'),
        totalOriginalPrice: parseFloat(item.totalOriginalPrice || '0'),
        totalDiscount:
          parseFloat(item.totalCardDeduction || '0') +
          parseFloat(item.totalCouponDeduction || '0'),
      };

      trendMap.set(period, existing);
    });

    // 转换为数组并计算总计
    const trendData = Array.from(trendMap.values()).map((item: any) => ({
      ...item,
      totalRevenue:
        item.mainOrder.totalRevenue + item.additionalService.totalRevenue,
      totalOriginalPrice:
        item.mainOrder.totalOriginalPrice +
        item.additionalService.totalOriginalPrice,
      totalDiscount:
        item.mainOrder.totalDiscount + item.additionalService.totalDiscount,
      totalOrderCount:
        item.mainOrder.orderCount + item.additionalService.orderCount,
    }));

    return trendData;
  }

  /**
   * 获取服务收入统计
   */
  async getServiceRevenueStatistics(
    startDate?: string,
    endDate?: string,
    serviceTypeId?: number,
    serviceId?: number,
    page = 1,
    pageSize = 20,
    sortBy: 'netRevenue' | 'totalOrderCount' | 'avgOrderValue' = 'netRevenue',
    sortOrder: 'asc' | 'desc' = 'desc'
  ) {
    // 构建查询条件 - 包含所有相关状态的订单
    let whereClause = `
      WHERE o.status IN ('已完成', '已评价', '退款中', '已退款')
    `;

    const params: any[] = [];

    if (startDate && endDate) {
      whereClause += ' AND o.createdAt BETWEEN ? AND ?';
      params.push(new Date(startDate), new Date(endDate + ' 23:59:59'));
    }

    if (serviceTypeId) {
      whereClause += ' AND s.serviceTypeId = ?';
      params.push(serviceTypeId);
    }

    if (serviceId) {
      whereClause += ' AND od.serviceId = ?';
      params.push(serviceId);
    }

    // 构建排序字段
    let orderField = 'netRevenue';
    switch (sortBy) {
      case 'netRevenue':
        orderField = 'netRevenue';
        break;
      case 'totalOrderCount':
        orderField = 'totalOrderCount';
        break;
      case 'avgOrderValue':
        orderField = 'avgOrderValue';
        break;
      default:
        orderField = 'netRevenue';
    }

    // 使用原生SQL查询获取服务收入统计
    const query = `
      SELECT
        od.serviceId,
        od.serviceName,
        od.servicePrice as basePrice,
        st.name as serviceType,
        st.id as serviceTypeId,
        COUNT(od.id) as totalOrderCount,
        COUNT(CASE WHEN o.status IN ('退款中', '已退款') THEN 1 ELSE NULL END) as refundedOrderCount,
        SUM(od.servicePrice) as totalOriginalPrice,
        SUM(CASE WHEN o.originalPrice > 0 THEN (od.servicePrice * (o.cardDeduction + o.couponDeduction)) / o.originalPrice ELSE 0 END) as totalDiscount,
        SUM(CASE WHEN o.originalPrice > 0 THEN od.servicePrice - (od.servicePrice * (o.cardDeduction + o.couponDeduction)) / o.originalPrice ELSE od.servicePrice END) as totalPaidAmount,
        SUM(CASE WHEN o.status IN ('退款中', '已退款') AND o.originalPrice > 0 THEN (od.servicePrice - (od.servicePrice * (o.cardDeduction + o.couponDeduction)) / o.originalPrice)
             WHEN o.status IN ('退款中', '已退款') THEN od.servicePrice ELSE 0 END) as totalRefundedAmount,
        SUM(CASE WHEN o.status IN ('已完成', '已评价') AND o.originalPrice > 0 THEN (od.servicePrice - (od.servicePrice * (o.cardDeduction + o.couponDeduction)) / o.originalPrice)
             WHEN o.status IN ('已完成', '已评价') THEN od.servicePrice ELSE 0 END) as netRevenue,
        AVG(CASE WHEN o.originalPrice > 0 THEN od.servicePrice - (od.servicePrice * (o.cardDeduction + o.couponDeduction)) / o.originalPrice ELSE od.servicePrice END) as avgOrderValue
      FROM order_details od
      INNER JOIN orders o ON od.orderId = o.id
      LEFT JOIN services s ON od.serviceId = s.id
      LEFT JOIN service_types st ON s.serviceTypeId = st.id
      ${whereClause}
      GROUP BY od.serviceId, od.serviceName, od.servicePrice, st.id, st.name
      ORDER BY ${orderField} ${sortOrder.toUpperCase()}
      LIMIT ? OFFSET ?
    `;

    params.push(pageSize, (page - 1) * pageSize);

    const serviceStats = (await Order.sequelize.query(query, {
      replacements: params,
      type: QueryTypes.SELECT,
    })) as any[];

    // 获取总数
    const countQuery = `
      SELECT COUNT(DISTINCT od.serviceId) as total
      FROM order_details od
      INNER JOIN orders o ON od.orderId = o.id
      LEFT JOIN services s ON od.serviceId = s.id
      LEFT JOIN service_types st ON s.serviceTypeId = st.id
      ${whereClause}
    `;

    const countParams = params.slice(0, -2); // 移除LIMIT和OFFSET参数
    const totalResult = (await Order.sequelize.query(countQuery, {
      replacements: countParams,
      type: QueryTypes.SELECT,
    })) as any[];

    const formattedStats = serviceStats.map((item: any) => {
      const totalOriginalPrice = parseFloat(item.totalOriginalPrice || '0');
      const totalDiscount = parseFloat(item.totalDiscount || '0');
      const totalPaidAmount = parseFloat(item.totalPaidAmount || '0');
      const totalRefundedAmount = parseFloat(item.totalRefundedAmount || '0');
      const netRevenue = parseFloat(item.netRevenue || '0');
      const discountRate =
        totalOriginalPrice > 0
          ? ((totalDiscount / totalOriginalPrice) * 100).toFixed(2)
          : '0.00';

      return {
        serviceId: item.serviceId,
        serviceName: item.serviceName,
        serviceType: item.serviceType || '未知类型',
        serviceTypeId: item.serviceTypeId,
        totalOrderCount: parseInt(item.totalOrderCount || '0'),
        refundedOrderCount: parseInt(item.refundedOrderCount || '0'),
        totalOriginalPrice: totalOriginalPrice.toFixed(2),
        totalDiscount: totalDiscount.toFixed(2),
        discountRate,
        totalPaidAmount: totalPaidAmount.toFixed(2),
        totalRefundedAmount: totalRefundedAmount.toFixed(2),
        netRevenue: netRevenue.toFixed(2),
        avgOrderValue: parseFloat(item.avgOrderValue || '0').toFixed(2),
        basePrice: parseFloat(item.basePrice || '0').toFixed(2),
      };
    });

    return {
      list: formattedStats,
      total: parseInt(totalResult[0]?.total || '0'),
      page,
      pageSize,
    };
  }

  /**
   * 获取员工收入统计
   */
  async getEmployeeRevenueStatistics(
    startDate?: string,
    endDate?: string,
    employeeId?: number,
    page = 1,
    pageSize = 20,
    sortBy: 'netRevenue' | 'totalOrderCount' | 'avgOrderValue' = 'netRevenue',
    sortOrder: 'asc' | 'desc' = 'desc'
  ) {
    // 构建查询条件和参数
    const baseParams: any[] = [];
    let timeConditionMain = '';
    let timeConditionAdditional = '';
    let employeeCondition = '';

    if (startDate && endDate) {
      timeConditionMain = 'AND o.createdAt BETWEEN ? AND ?';
      timeConditionAdditional = 'AND aso.createdAt BETWEEN ? AND ?';
      // 为主订单时间条件添加参数
      baseParams.push(new Date(startDate), new Date(endDate + ' 23:59:59'));
      // 为追加服务时间条件添加参数
      baseParams.push(new Date(startDate), new Date(endDate + ' 23:59:59'));
    }

    if (employeeId) {
      employeeCondition = 'AND e.id = ?';
      baseParams.push(employeeId);
    }

    // 构建排序字段
    let orderField = 'netRevenue';
    switch (sortBy) {
      case 'netRevenue':
        orderField = 'netRevenue';
        break;
      case 'totalOrderCount':
        orderField = 'totalOrderCount';
        break;
      case 'avgOrderValue':
        orderField = 'avgOrderValue';
        break;
      default:
        orderField = 'netRevenue';
    }

    // 分别查询主订单和追加服务数据，然后在应用层合并
    // 1. 查询主订单数据
    const mainOrderQuery = `
      SELECT
        e.id as employeeId,
        e.name,
        e.phone,
        e.avatar,
        e.rating,
        COUNT(o.id) as mainOrderCount,
        COALESCE(SUM(o.originalPrice), 0) as mainTotalOriginalPrice,
        COALESCE(SUM(o.cardDeduction + o.couponDeduction), 0) as mainTotalDiscount,
        COALESCE(SUM(o.totalFee), 0) as mainTotalPaidAmount,
        COALESCE(SUM(CASE WHEN o.status IN ('退款中', '已退款') THEN o.totalFee ELSE 0 END), 0) as mainTotalRefundedAmount,
        COALESCE(SUM(CASE WHEN o.status IN ('已完成', '已评价') THEN o.totalFee ELSE 0 END), 0) as mainNetRevenue
      FROM employees e
      LEFT JOIN orders o ON e.id = o.employeeId AND o.status IN ('已完成', '已评价', '退款中', '已退款') ${timeConditionMain}
      WHERE 1=1 ${employeeCondition}
      GROUP BY e.id, e.name, e.phone, e.avatar, e.rating
    `;

    // 2. 查询追加服务数据
    const additionalServiceQuery = `
      SELECT
        e.id as employeeId,
        COUNT(aso.id) as additionalOrderCount,
        COALESCE(SUM(aso.originalPrice), 0) as additionalTotalOriginalPrice,
        COALESCE(SUM(aso.cardDeduction + aso.couponDeduction), 0) as additionalTotalDiscount,
        COALESCE(SUM(aso.totalFee), 0) as additionalTotalPaidAmount,
        COALESCE(SUM(CASE WHEN aso.status IN ('refunding', 'refunded') THEN aso.totalFee ELSE 0 END), 0) as additionalTotalRefundedAmount,
        COALESCE(SUM(CASE WHEN aso.status = 'completed' THEN aso.totalFee ELSE 0 END), 0) as additionalNetRevenue
      FROM employees e
      LEFT JOIN additional_service_orders aso ON e.id = aso.employeeId AND aso.status IN ('completed', 'refunding', 'refunded') ${timeConditionAdditional}
      WHERE 1=1 ${employeeCondition}
      GROUP BY e.id
    `;

    // 执行查询
    const mainOrderStats = (await Order.sequelize.query(mainOrderQuery, {
      replacements: baseParams,
      type: QueryTypes.SELECT,
    })) as any[];

    const additionalServiceStats = (await Order.sequelize.query(
      additionalServiceQuery,
      {
        replacements: baseParams,
        type: QueryTypes.SELECT,
      }
    )) as any[];

    // 合并数据
    const employeeMap = new Map();

    // 处理主订单数据
    mainOrderStats.forEach((item: any) => {
      employeeMap.set(item.employeeId, {
        employeeId: item.employeeId,
        name: item.name,
        phone: item.phone,
        avatar: item.avatar,
        rating: parseFloat(item.rating || '0'),
        mainOrderCount: parseInt(item.mainOrderCount || '0'),
        mainTotalOriginalPrice: parseFloat(item.mainTotalOriginalPrice || '0'),
        mainTotalDiscount: parseFloat(item.mainTotalDiscount || '0'),
        mainTotalPaidAmount: parseFloat(item.mainTotalPaidAmount || '0'),
        mainTotalRefundedAmount: parseFloat(
          item.mainTotalRefundedAmount || '0'
        ),
        mainNetRevenue: parseFloat(item.mainNetRevenue || '0'),
        additionalOrderCount: 0,
        additionalTotalOriginalPrice: 0,
        additionalTotalDiscount: 0,
        additionalTotalPaidAmount: 0,
        additionalTotalRefundedAmount: 0,
        additionalNetRevenue: 0,
      });
    });

    // 处理追加服务数据
    additionalServiceStats.forEach((item: any) => {
      const existing = employeeMap.get(item.employeeId);
      if (existing) {
        existing.additionalOrderCount = parseInt(
          item.additionalOrderCount || '0'
        );
        existing.additionalTotalOriginalPrice = parseFloat(
          item.additionalTotalOriginalPrice || '0'
        );
        existing.additionalTotalDiscount = parseFloat(
          item.additionalTotalDiscount || '0'
        );
        existing.additionalTotalPaidAmount = parseFloat(
          item.additionalTotalPaidAmount || '0'
        );
        existing.additionalTotalRefundedAmount = parseFloat(
          item.additionalTotalRefundedAmount || '0'
        );
        existing.additionalNetRevenue = parseFloat(
          item.additionalNetRevenue || '0'
        );
      } else {
        // 如果员工只有追加服务，没有主订单，需要获取员工信息
        // 这种情况下，我们需要单独查询员工信息
        employeeMap.set(item.employeeId, {
          employeeId: item.employeeId,
          name: null, // 需要单独查询
          phone: null,
          avatar: null,
          rating: 0,
          mainOrderCount: 0,
          mainTotalOriginalPrice: 0,
          mainTotalDiscount: 0,
          mainTotalPaidAmount: 0,
          mainTotalRefundedAmount: 0,
          mainNetRevenue: 0,
          additionalOrderCount: parseInt(item.additionalOrderCount || '0'),
          additionalTotalOriginalPrice: parseFloat(
            item.additionalTotalOriginalPrice || '0'
          ),
          additionalTotalDiscount: parseFloat(
            item.additionalTotalDiscount || '0'
          ),
          additionalTotalPaidAmount: parseFloat(
            item.additionalTotalPaidAmount || '0'
          ),
          additionalTotalRefundedAmount: parseFloat(
            item.additionalTotalRefundedAmount || '0'
          ),
          additionalNetRevenue: parseFloat(item.additionalNetRevenue || '0'),
        });
      }
    });

    // 过滤出有订单的员工并计算总计
    const employeeStats = Array.from(employeeMap.values())
      .filter(item => item.mainOrderCount + item.additionalOrderCount > 0)
      .map(item => ({
        ...item,
        totalOriginalPrice:
          item.mainTotalOriginalPrice + item.additionalTotalOriginalPrice,
        totalDiscount: item.mainTotalDiscount + item.additionalTotalDiscount,
        totalPaidAmount:
          item.mainTotalPaidAmount + item.additionalTotalPaidAmount,
        totalRefundedAmount:
          item.mainTotalRefundedAmount + item.additionalTotalRefundedAmount,
        netRevenue: item.mainNetRevenue + item.additionalNetRevenue,
        totalOrderCount: item.mainOrderCount + item.additionalOrderCount,
      }));

    // 排序
    employeeStats.sort((a, b) => {
      const aValue = a[orderField] || 0;
      const bValue = b[orderField] || 0;
      return sortOrder === 'desc' ? bValue - aValue : aValue - bValue;
    });

    // 分页
    const startIndex = (page - 1) * pageSize;
    const paginatedStats = employeeStats.slice(
      startIndex,
      startIndex + pageSize
    );

    const formattedStats = paginatedStats.map((item: any) => {
      const totalOriginalPrice = parseFloat(item.totalOriginalPrice || '0');
      const totalDiscount = parseFloat(item.totalDiscount || '0');
      const totalPaidAmount = parseFloat(item.totalPaidAmount || '0');
      const totalRefundedAmount = parseFloat(item.totalRefundedAmount || '0');
      const netRevenue = parseFloat(item.netRevenue || '0');
      const totalOrderCount = parseInt(item.totalOrderCount || '0');
      const discountRate =
        totalOriginalPrice > 0
          ? ((totalDiscount / totalOriginalPrice) * 100).toFixed(2)
          : '0.00';

      // 主订单数据
      const mainOrderCount = parseInt(item.mainOrderCount || '0');
      const mainTotalOriginalPrice = parseFloat(
        item.mainTotalOriginalPrice || '0'
      );
      const mainTotalDiscount = parseFloat(item.mainTotalDiscount || '0');
      const mainTotalPaidAmount = parseFloat(item.mainTotalPaidAmount || '0');
      const mainTotalRefundedAmount = parseFloat(
        item.mainTotalRefundedAmount || '0'
      );
      const mainNetRevenue = parseFloat(item.mainNetRevenue || '0');

      // 追加服务数据
      const additionalOrderCount = parseInt(item.additionalOrderCount || '0');
      const additionalTotalOriginalPrice = parseFloat(
        item.additionalTotalOriginalPrice || '0'
      );
      const additionalTotalDiscount = parseFloat(
        item.additionalTotalDiscount || '0'
      );
      const additionalTotalPaidAmount = parseFloat(
        item.additionalTotalPaidAmount || '0'
      );
      const additionalTotalRefundedAmount = parseFloat(
        item.additionalTotalRefundedAmount || '0'
      );
      const additionalNetRevenue = parseFloat(item.additionalNetRevenue || '0');

      return {
        employeeId: item.employeeId,
        employee: {
          id: item.employeeId,
          name: item.name,
          phone: item.phone,
          avatar: item.avatar,
          rating: parseFloat(item.rating || '0'),
        },
        mainOrder: {
          orderCount: mainOrderCount,
          totalOriginalPrice: mainTotalOriginalPrice.toFixed(2),
          totalDiscount: mainTotalDiscount.toFixed(2),
          totalPaidAmount: mainTotalPaidAmount.toFixed(2),
          totalRefundedAmount: mainTotalRefundedAmount.toFixed(2),
          netRevenue: mainNetRevenue.toFixed(2),
          avgOrderValue:
            mainOrderCount > 0
              ? (mainTotalPaidAmount / mainOrderCount).toFixed(2)
              : '0.00',
        },
        additionalService: {
          orderCount: additionalOrderCount,
          totalOriginalPrice: additionalTotalOriginalPrice.toFixed(2),
          totalDiscount: additionalTotalDiscount.toFixed(2),
          totalPaidAmount: additionalTotalPaidAmount.toFixed(2),
          totalRefundedAmount: additionalTotalRefundedAmount.toFixed(2),
          netRevenue: additionalNetRevenue.toFixed(2),
          avgOrderValue:
            additionalOrderCount > 0
              ? (additionalTotalPaidAmount / additionalOrderCount).toFixed(2)
              : '0.00',
        },
        totalOriginalPrice: totalOriginalPrice.toFixed(2),
        totalDiscount: totalDiscount.toFixed(2),
        discountRate,
        totalPaidAmount: totalPaidAmount.toFixed(2),
        totalRefundedAmount: totalRefundedAmount.toFixed(2),
        netRevenue: netRevenue.toFixed(2),
        totalOrderCount,
        avgOrderValue:
          totalOrderCount > 0
            ? (totalPaidAmount / totalOrderCount).toFixed(2)
            : '0.00',
      };
    });

    return {
      list: formattedStats,
      total: employeeStats.length, // 使用实际的员工数量
      page,
      pageSize,
    };
  }
}
