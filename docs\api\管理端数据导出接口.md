# 管理端数据导出接口

## 1. 员工信息导出

### 1.1 导出员工信息Excel
- **接口**: `GET /admin/employees/export`
- **描述**: 导出员工信息列表为Excel文件，支持筛选条件
- **参数**:
  - `name` (string): 员工姓名模糊查询
  - `phone` (string): 手机号模糊查询
  - `status` (number): 员工状态筛选（0-离职，1-在职）
  - `position` (string): 职位模糊查询
- **返回**: Excel文件下载
- **Excel内容**:
  - 标题：员工信息导出列表
  - 列：员工ID、姓名、手机号、职位、接单等级、工作经验(月)、服务评分、钱包余额、推广码、入职时间、离职时间、状态、车辆信息
  - 文件名格式：`employees_YYYYMMDD_HHmmss.xlsx`

## 2. 用户信息导出

### 2.1 导出用户信息Excel
- **接口**: `GET /admin/customers/export`
- **描述**: 导出用户信息列表为Excel文件，支持筛选条件
- **参数**:
  - `phone` (string): 手机号模糊查询
  - `nickname` (string): 昵称模糊查询
  - `memberStatus` (number): 会员状态筛选（0-普通会员，1-权益会员）
  - `status` (number): 用户状态筛选（0-禁用，1-启用）
- **返回**: Excel文件下载
- **Excel内容**:
  - 标题：用户信息导出列表
  - 列：用户ID、昵称、手机号、性别、详细地址、收货地址、会员状态、积分值、推广码、推广员工、最后登录时间、注册时间、状态
  - 文件名格式：`customers_YYYYMMDD_HHmmss.xlsx`

## 使用说明

### 员工导出功能特点
- 支持按条件筛选导出，与列表查询条件保持一致
- 导出所有符合条件的数据，不受分页限制
- Excel格式输出，包含完整的员工信息
- 自动生成带时间戳的文件名
- 支持中文列名和数据格式化

### 员工筛选条件说明
- **姓名筛选**: 支持模糊匹配，可输入姓名的任意部分
- **手机号筛选**: 支持模糊匹配，可输入手机号的任意部分
- **状态筛选**: 0表示离职员工，1表示在职员工
- **职位筛选**: 支持模糊匹配，如"美容师"、"洗护师"等

### 员工数据格式说明
- **接单等级**: 1-5级数字显示
- **工作经验**: 以月为单位的数字
- **服务评分**: 保留1位小数，范围0-5分
- **钱包余额**: 保留2位小数的金额格式
- **日期格式**: YYYY-MM-DD格式
- **车辆信息**: 车牌号(状态)的组合格式

### 用户导出功能特点
- 支持按条件筛选导出，与列表查询条件保持一致
- 导出所有符合条件的数据，不受分页限制
- Excel格式输出，包含完整的用户信息
- 自动生成带时间戳的文件名
- 支持中文列名和数据格式化

### 用户筛选条件说明
- **手机号筛选**: 支持模糊匹配，可输入手机号的任意部分
- **昵称筛选**: 支持模糊匹配，可输入昵称的任意部分
- **会员状态筛选**: 0表示普通会员，1表示权益会员
- **用户状态筛选**: 0表示禁用用户，1表示启用用户

### 用户数据格式说明
- **性别显示**: 0-未知，1-男，2-女
- **详细地址**: 用户在个人信息中填写的地址
- **收货地址**: 用户的默认收货地址（省市区+详细地址），优先显示默认地址，无默认地址时显示第一个地址
- **会员状态**: 普通会员/权益会员
- **用户状态**: 启用/禁用
- **积分值**: 整数显示
- **时间格式**: YYYY-MM-DD HH:mm:ss格式
- **推广员工**: 显示推广该用户的员工姓名

### 业务规则说明
- 用户注册后默认为普通会员
- 购买权益卡后变为权益会员
- 权益卡过期或使用次数用完后变回普通会员
- 每个用户只能被一个员工推广
- 推广关系建立后不可更改
