import { Body, Controller, Inject, Put } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { CustomerService } from '../../service/customer.service';
import { Customer } from '../../entity';
import { CustomError } from '../../error/custom.error';
import { TokenPayload } from '../../interface';

@Controller('/customer/profile')
export class CustomerProfileController {
  @Inject()
  ctx: Context;

  @Inject()
  customerService: CustomerService;

  @Put('/', { summary: '用户端修改个人信息' })
  async updateProfile(@Body() body: { nickname?: string; avatar?: string }) {
    // 获取当前登录的用户信息
    const currentUser = this.ctx.state.user as TokenPayload;

    if (!currentUser || currentUser.userType !== 'customer') {
      throw new CustomError('请先登录用户账号');
    }

    const { nickname, avatar } = body;

    // 验证参数
    if (!nickname && !avatar) {
      throw new CustomError('请提供要修改的信息');
    }

    // 验证昵称长度
    if (nickname && (nickname.length < 1 || nickname.length > 20)) {
      throw new CustomError('昵称长度应在1-20个字符之间');
    }

    // 验证头像URL格式
    if (avatar && !avatar.startsWith('http') && !avatar.startsWith('//')) {
      throw new CustomError('头像必须是有效的URL地址');
    }

    // 构建更新数据
    const updateData: any = {};
    if (nickname) {
      updateData.nickname = nickname;
    }
    if (avatar) {
      updateData.avatar = avatar;
    }

    // 更新用户信息
    await this.customerService.update({ id: currentUser.userId }, updateData);

    // 返回更新后的用户信息
    const updatedCustomer = await Customer.findByPk(currentUser.userId, {
      attributes: [
        'id',
        'nickname',
        'avatar',
        'phone',
        'memberStatus',
        'points',
      ],
    });

    if (!updatedCustomer) {
      throw new CustomError('用户信息不存在');
    }

    return {
      id: updatedCustomer.id,
      nickname: updatedCustomer.nickname,
      avatar: updatedCustomer.avatar,
      phone: updatedCustomer.phone,
      memberStatus: updatedCustomer.memberStatus,
      points: updatedCustomer.points,
    };
  }
}
